import  os
import datetime
from datetime import datetime
import  time

import json
import uuid
import redis
import sqlite3
import traceback

import redis
from time import sleep
import AteRedis as CommonUser  #Redis
from ATE_WriteLog import __writeToLog as __writeToLog  #日志
from json import JSONEncoder

conWriteerr=11  #写异常
conReadErr=22   #读异常
conFuncErr=33  #抛出异常

ConFileName='DCLoad'

class CustomEncoder(JSONEncoder):
    def default(self, obj):
        try:
            # 尝试常规序列化
            return super().default(obj)
        except TypeError:
            # 处理CDispatch对象
            if hasattr(obj, '__class__') and 'CDispatch' in str(obj.__class__):
                return str(obj)  # 转换为字符串
            raise

#查找Sqlite中的脚本名称,根据输入的设备别名
#参数： Sqlite文件地址    设备别名
def __find_code_filename(database_path, search_name):
    """
    查询SQLite数据库，返回CustomName包含指定模式的记录的CodeFileName值

    :param database_path: SQLite数据库文件路径
    :param search_pattern: 要搜索的CustomName模式，默认为"E5000:001"
    :return: 匹配记录的CodeFileName列表
    """
    try:
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()

        # 使用 = 进行精确匹配
        cursor.execute("""
                    SELECT CodeFileName 
                    FROM Devices 
                    WHERE CustomName = ?
                    """, (search_name,))  # 注意参数化查询的逗号

        return [row[0] for row in cursor.fetchall()]

    except sqlite3.Error as e:
        print(f"数据库错误: {e}")
        return []
    finally:
        # 确保连接关闭
        if conn:
            conn.close()


# 创建Redis连接

# def RedisConnect():
#     #redisSession = redis.Redis(host='localhost', port=6379, db=0)
#     pool = redis.ConnectionPool(host='localhost', port=6379, db=0)
#     redisSession = redis.Redis(connection_pool=pool)
#     if redisSession:
#         return redisSession
#     else:
#         return None

#去除字符串的""
def __remove_quotes(s):
    return s.strip('"') if isinstance(s, str) else s

def __GetDevJson(vardeviceAliase,varsDataMode,varArgs=None):
    try:
        # 构造消息数据
        scriptName = 'S7200.py'
        DataSet = {}
        DataSet['script'] = scriptName
        DataSet['method'] = 'set'
        DataSet['request_id'] = "uuid-1234"
        DataSet['timeout'] = 1
        DataSet['delay'] = 100
        DataSet['args'] = {}
        if varsDataMode=='Run':
            DataSet['Command'] = 'run_device'
            DataSet['args'] = {}
        elif varsDataMode=='Stop':
            DataSet['Command'] = 'stop_device'
            DataSet['args'] = {}
        elif varsDataMode=='Reset':
            DataSet['Command'] = 'reset_device'
            DataSet['args'] = {}
        elif varsDataMode == 'Remote':  #远程
            DataSet['Command'] = 'Remote_device'
            DataSet['args'] = varArgs
        elif varsDataMode == 'ParasSet_ProtectData':  #远程
            DataSet['Command'] = 'ParasSet_ProtectData'
            DataSet['args'] = varArgs
        elif varsDataMode == 'ParasSet':  #远程
            DataSet['Command'] = 'ParasSet'
            DataSet['args'] = varArgs
        elif varsDataMode == 'Short':  # 远程
            DataSet['Command'] = 'Short'
            DataSet['args'] = varArgs
        elif varsDataMode == 'Source_CC':  # 远程
            DataSet['Command'] = 'Source_CC'
            DataSet['args'] = varArgs
        elif varsDataMode == 'Source_CV':  # 远程
            DataSet['Command'] = 'Source_CV'
            DataSet['args'] = varArgs
        elif varsDataMode == 'Load_CC':  # 远程
            DataSet['Command'] = 'Load_CC'
            DataSet['args'] = varArgs
        elif varsDataMode == 'Load_CV':  # 远程
            DataSet['Command'] = 'Load_CV'
            DataSet['args'] = varArgs
        elif varsDataMode == 'Load_CR':  # 远程
            DataSet['Command'] = 'Load_CR'
            DataSet['args'] = varArgs
####################################################################################################
        # 将数据放入列表中
        requests = [DataSet]
        # 转换为JSON字符串
        json_string = json.dumps(requests, indent=4,cls=CustomEncoder)#,cls=CustomEncoder
        return json_string
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__GetDevJson error ' + errorStr,ConFileName)
        traceback_message = traceback.format_exc()
        __writeToLog('__GetDevJson error ' + traceback_message, ConFileName)
        return None

#"E5000LocalVisa2.py" "device:cmd:E50002"
def __DC_SourceLoad_ONxx(deviceAliase,redisSession,arg1,seq_context):
    try:
        __writeToLog(f"设备别名是{deviceAliase}",ConFileName)
        # deviceAliase=__remove_quotes(deviceAliase)  #让“E5000" 变成E5000  不然用“E5000"发布消息的键值不对  目前类型改成了string  不需要了

        if seq_context:
            readTime=seq_context.EvaluateEx(arg1,0)
        else:
            readTime =0
        __writeToLog(f"读取的变量arg1值为{readTime}",ConFileName)

        if 11>111:
            db_path = r"C:\Users\<USER>\Desktop\data.db"

            filenames = __find_code_filename(db_path, deviceAliase)
            if filenames:
                scriptName=filenames[0]
                __writeToLog(f'找到了设备别名{deviceAliase}对应的{scriptName}脚本文件',ConFileName)
            else:
                __writeToLog(f'未找到设备别名{deviceAliase}对应的脚本文件',ConFileName)
                scriptName="111"
        else:
            scriptName='S7200_3' #deviceAliase
            scriptName ='S7200.py'
        # 构造消息数据
        data = {
            "script": scriptName,
            "method": "set",
            "Command": "run_device",
            #"args": {"arg1":"1.3","arg2":12,"arg3":"CV"},
            "args": {},
            "request_id": "uuid-1234",
            "timeout": 1,
            "delay": 100
        }
        argss={}
        argss['arg1'] =1.3
        argss['arg2'] =12
        argss['arg3'] = "CV"
        data['args']=argss

        # 将数据放入列表中
        requests = [data]

        # 转换为JSON字符串
        json_string = json.dumps(requests, indent=4)
        __writeToLog('ee'+json_string,ConFileName)

        # 发布消息的频道
        #channel_name = "device:cmd:E50002"
        channel_name =f"device:cmd:{deviceAliase}"
        print('channel_name',channel_name)

        start = time.time()
        # 发布消息
        clients_received = redisSession.publish(channel_name, json_string)

        print(f"发布耗时: {(time.time() - start) * 1000:.2f}ms")  # 应 < 5ms（本地）
        print(f"消息已发布到频道 {channel_name}，接收客户端数: {clients_received}")
        __writeToLog(f"消息已发布到频道 {channel_name}，接收客户端数: {clients_received}",ConFileName)
    except Exception as e:
        errorStr = str(e)
        __writeToLog('Set_E5000 redis Publish state error' + errorStr,ConFileName)

#订阅
def __DC_SourceLoad_subscribe(deviceAliase,redisSession,seq_context):
    try:
        idevName = deviceAliase
        idevName ='DCLoad'
        channel = ["device:ack:" + idevName, "device:response:" + idevName]
        rr = CommonUser.__subscribe_channel(redisSession, channel)  # 订阅
        return rr
    except Exception as e:
        errorStr = str(e)
        __writeToLog('Set_E5000 redis Publish state error' + errorStr,ConFileName)
        return None

def DC_SourceLoad_ON(deviceAliase,redisSession,redisSub,seq_context):
    try:
        idevName = deviceAliase
        sJson= __GetDevJson('','Run')
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_ON error ' + errorStr,ConFileName)

def DC_SourceLoad_STOP(deviceAliase,redisSession,redisSub,seq_context):
    try:
        idevName = deviceAliase
        sJson= __GetDevJson('','Stop')
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_STOP error ' + errorStr,ConFileName)

def DC_SourceLoad_Reset(deviceAliase,redisSession,redisSub,seq_context):
    try:
        idevName = deviceAliase
        sJson= __GetDevJson('','Reset')
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_Reset error ' + errorStr,ConFileName)

def DC_SourceLoad_Remote_On(deviceAliase,redisSession,redisSub,seq_context):
    try:
        idevName = deviceAliase
        args={}
        args['Remote']='On'
        sJson= __GetDevJson('','Remote',args)
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_Remote_On error ' + errorStr,ConFileName)

def DC_SourceLoad_Remote_Off(deviceAliase,redisSession,redisSub,seq_context):
    try:
        idevName = deviceAliase
        args={}
        args['Remote']='Off'
        sJson= __GetDevJson('','Remote',args)
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_Remote_Off error ' + errorStr,ConFileName)

def DC_SourceLoad_ParasSet_ProtectData(deviceAliase,redisSession,redisSub,seq_context,OVPValue,OCPValue,OPPValue):
    try:
        idevName = deviceAliase
        args={}
        if seq_context:
            args['OVP']=seq_context.EvaluateEx(OVPValue,0)
            args['OCP'] = seq_context.EvaluateEx(OCPValue,0)
            args['OPP'] = seq_context.EvaluateEx(OPPValue,0)
        else:
            args['OVP']=OVPValue
            args['OCP'] = OCPValue
            args['OPP'] = OPPValue
        #json_string = json.dumps(args, indent=4,cls=CustomEncoder)
        #__writeToLog('eee'+json_string,ConFileName)
        sJson= __GetDevJson('','ParasSet_ProtectData',args)
        #__writeToLog(sJson,ConFileName)
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_ParasSet_ProtectData error ' + errorStr,ConFileName)

#大全版设置，界面同之前
def DC_SourceLoad_ParasSet(deviceAliase,redisSession,redisSub,seq_context):
    try:
        idevName = deviceAliase
        args={}
        if seq_context:
            mode_num = seq_context.GetValNumber("Step.设置.设备模式",0)
            soure_mode_str = seq_context.GetValString("Step.设置.电源模式","0")
            load_mode_str = seq_context.GetValString("Step.设置.负载模式","0")
            #电源
            DC_CC_Cur_Set = seq_context.GetValNumber("Step.设置.电源电流", 0)#CC电流值设定
            U_Max_Set = seq_context.GetValNumber("Step.设置.电压上限", 0)
            U_Min_Set = seq_context.GetValNumber("Step.设置.电压下限", 0)

            DC_CV_Vol_Set = seq_context.GetValNumber("Step.设置.电源电压", 0)#CV电压值设定
            I_Max_Set = seq_context.GetValNumber("Step.设置.电流上限", 0)
            I_Min_Set = seq_context.GetValNumber("Step.设置.电流下限", 0)

            P_Max_Set = seq_context.GetValNumber("Step.设置.功率上限", 0)
            P_Min_Set = seq_context.GetValNumber("Step.设置.功率下限", 0)
            #负载
            short = seq_context.GetValNumber("Step.设置.负载短路", 0)#短路设置

            Load_CC_Cur_Set = seq_context.GetValNumber("Step.设置.负载电流", 0)#CC电流值设定

            Load_CV_Vol_Set = seq_context.GetValNumber("Step.设置.负载电压", 0)#CV电压值设定

            Load_CR_Res_Set = seq_context.GetValNumber("Step.设置.负载电阻", 0)#CR电阻值设定

            DataSet['args']['mode'] = mode_num
            DataSet['args']['soure_mode'] = soure_mode_str
            DataSet['args']['load_mode'] = load_mode_str
            DataSet['args']['DC_Cur'] = DC_CC_Cur_Set
            DataSet['args']['U_Max'] = U_Max_Set
            DataSet['args']['U_Min'] = U_Min_Set
            DataSet['args']['DC_Vol'] = DC_CV_Vol_Set

            DataSet['args']['I_Min'] = I_Min_Set
            DataSet['args']['I_Max'] = I_Max_Set
            DataSet['args']['P_Max'] = P_Max_Set
            DataSet['args']['P_Min'] = P_Min_Set
            DataSet['args']['short'] = short
            DataSet['args']['Load_Cur'] = Load_CC_Cur_Set
            DataSet['args']['Load_Vol'] = Load_CV_Vol_Set
            DataSet['args']['Load_R'] = Load_CR_Res_Set

        sJson= __GetDevJson('','ParasSet',args)
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_ParasSet_ProtectData error ' + errorStr,ConFileName)

def DC_SourceLoad_Short(deviceAliase,redisSession,redisSub,seq_context,ShortMode):
    try:
        idevName = deviceAliase
        args = {}
        args['Short']=ShortMode
        sJson= __GetDevJson('','Short',args)
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_Short error ' + errorStr,ConFileName)


def DC_SourceLoad_Source_CC(deviceAliase,redisSession,redisSub,seq_context,Cur_Set,U_Max_Set,U_Min_Set,P_Max_Set,P_Min_Set):
    try:
        idevName = deviceAliase
        args = {}
        args['Cur_Set']=Cur_Set
        args['U_Max_Set']=U_Max_Set
        args['U_Min_Set']=U_Min_Set
        args['P_Max_Set']=P_Max_Set
        args['P_Min_Set']=P_Min_Set
        sJson= __GetDevJson('','Source_CC',args)
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_Source_CC error ' + errorStr,ConFileName)

def DC_SourceLoad_Source_CV(deviceAliase,redisSession,redisSub,seq_context,Vol_Set,I_Max_Set,I_Min_Set,P_Max_Set,P_Min_Set):
    try:
        idevName = deviceAliase
        args = {}
        args['Vol_Set']=Vol_Set
        args['I_Max_Set']=I_Max_Set
        args['I_Min_Set']=I_Min_Set
        args['P_Max_Set']=P_Max_Set
        args['P_Min_Set']=P_Min_Set
        sJson= __GetDevJson('','Source_CV',args)
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_Source_CV error ' + errorStr,ConFileName)

def DC_SourceLoad_CC(deviceAliase,redisSession,redisSub,seq_context,ModevalueSet):
    try:
        idevName = deviceAliase
        args = {}
        args['ModevalueSet']=ModevalueSet
        sJson= __GetDevJson('','Load_CC',args)
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_CC error ' + errorStr,ConFileName)

def DC_SourceLoad_CV(deviceAliase,redisSession,redisSub,seq_context,ModevalueSet):
    try:
        idevName = deviceAliase
        args = {}
        args['ModevalueSet']=ModevalueSet
        sJson= __GetDevJson('','Load_CV',args)
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_CV error ' + errorStr,ConFileName)

def DC_SourceLoad_CR(deviceAliase,redisSession,redisSub,seq_context,ModevalueSet):
    try:
        idevName = deviceAliase
        args = {}
        args['ModevalueSet']=ModevalueSet
        sJson= __GetDevJson('','Load_CR',args)
        iret=CommonUser.__pubAndSubToRedis(redisSession, redisSub, sJson, idevName)
        return iret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_CR error ' + errorStr,ConFileName)

#DC_Load_ParasSet_ProtectData('LL',None,None,None,11,22,33)
#读设备状态是Hash值
def DC_SourceLoad_Inquiry(deviceAliase,redisSession,seq_context):
    try:
        idevName=deviceAliase
        channel='device:status:'+idevName
        vardataDevice=CommonUser.userGet_redis_data(redisSession,channel)
        if (vardataDevice is not None) and (seq_context is not None):
            ##解析
            if 'State' in vardataDevice:
                value=int(vardataDevice['State'])
                if value & (1 << 4)==16:#查询第4位是否为1，如果是，则表示设备在线
                   seq_context.SetValBoolean("StationGlobals.Devices.DC_SourceLoad.Status.runState", True, True)
                else:
                   seq_context.SetValBoolean("StationGlobals.Devices.DC_SourceLoad.Status.runState", True, False)
            for key,val in vardataDevice.items():
                if type(val) == str:
                    seq_context.SetValString("StationGlobals.Devices.DC_SourceLoad.Status."+key, '1', val)
                if (type(val) == int) or (type(val) == float) :
                    seq_context.SetValNumber("StationGlobals.Devices.DC_SourceLoad.Status."+key, 1, val)
                if (type(val) == int) (type(val) == bool):
                    seq_context.SetValBoolean("StationGlobals.Devices.DC_SourceLoad.Status."+key, True, val)

            errRet=(conReadErr, '', False)
        else:
            errRet = (conReadErr, '', True)
        print('vardataDevice',vardataDevice)
        return errRet

    except Exception as e:
        errorStr = str(e)
        __writeToLog('DC_Load_Inquiry error ' + errorStr)
        return (conFuncErr, errorStr, True)



#读设备状态区分是，慢速Hash值  再加上 高速是Stream结构   目前未使用  因为设备只使用慢速方式写入
def __DC_SourceLoad_InquiryAll(deviceAliase,redisSession,seq_context,writeType):
    try:

        if writeType=="慢速":
            # 获取Hash所有字段和值
            hash_data = redisSession.hgetall(f"device:status:{deviceAliase}")

            if not hash_data:
                print(f"键 device:status:{deviceAliase} 不存在或为空")
                __writeToLog(f"慢速键 device:status:{deviceAliase} 不存在或为空",ConFileName)
                return

            for field, value in hash_data.items():
                # print(f"{field}: {value}")
                field_str = field.decode('utf-8')  # 解码字段名  如果不解码 前面会有个b标识符
                value_str = value.decode('utf-8')  # 解码值
                if field_str=='Vol':
                    print("777")
                    seq_context.SetValNumber("StationGlobals.Devices.DC_Load.Voltage", 0, float(value_str))
                    __writeToLog("777",ConFileName)
                else:
                    print(f'信号名为{field_str},信号值为{value_str}')
                    __writeToLog(f'信号名为{field_str},信号值为{value_str}',ConFileName)

        else:  #设备状态为高速写入方式
            try:
                # 读取Stream中所有消息（从最早到最新）
                messages =redisSession.xrange(f"device:status:{deviceAliase}")

                # # 只读取Stream中最新的一条消息（使用XREVRANGE + 限制数量）
                # messages = r.xrevrange("device:status:E5000:001", count=1)  # 修改这里

                if not messages:
                    print("Stream为空")
                    __writeToLog(f"高速键 device:status:{deviceAliase} 不存在或为空",ConFileName)
                    return

                print(f"共 {len(messages)} 条消息:")
                __writeToLog(f"共 {len(messages)} 条消息:",ConFileName)
                print("-" * 40)
                for msg_id, msg_data in messages:
                    print(f"ID: {msg_id}")
                    __writeToLog(f"消息ID: {msg_id}",ConFileName)
                    for field, value in msg_data.items():
                        field_str = field.decode('utf-8')  # 解码字段名  如果不解码 前面会有个b标识符
                        value_str = value.decode('utf-8')  # 解码值

                        if field_str=="Cur":
                            seq_context.SetValNumber("StationGlobals.Devices.DC_Load.Current", 0, float(value_str))
                        print(f"  {field_str}: {value_str}")
                        __writeToLog(f"  {field_str}: {value_str}",ConFileName)
                    print("-" * 40)

            except redis.RedisError as e:
                print(f"Redis操作失败: {e}")

    except Exception as e:
        errorStr = str(e)
        __writeToLog('Inquiry_E5000 redis HashValue error' + errorStr,ConFileName)


def exsample():
    #print(retRedis)
    #DC_Load_ON('S7200', retRedis, None, None)
    retRedis = CommonUser.get_redis_connection()
    retRedis2 = CommonUser.get_redis_connection()
    idevName = 'DCLoad'
    channel = ["device:ack:" + idevName, "device:response:" + idevName]
    rr = CommonUser.__subscribe_channel(retRedis, channel)  # 订阅
    for i in range(3):
        #DC_Load_ON(idevName, retRedis, None, None)
        formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        print('begin',formatted_time)
        #rr = CommonUser.__subscribe_channel(retRedis, channel)  # 异步检测
        #formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        #print('end',formatted_time)
        iret=DC_Load_ON('DCLoad',retRedis,rr,None)
        print(iret)
        #istr=__GetDevJson('','')
        #CommonUser.__pubAndSubToRedis(retRedis,rr,istr,idevName)  #发布和监听
        #CommonUser.__Unsubscribe_channel(rr,channel)

    sleep(2)
    CommonUser.redis_disconnection(retRedis)
    CommonUser.__Unsubscribe_channel(rr,channel)
    CommonUser.redis_disconnection(retRedis2)
#exsample()




