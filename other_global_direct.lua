-- other模块 - 直接使用全局变量版本
-- 可以直接使用 A, B, C，无需任何前缀

print("=== other_global_direct.lua 开始加载 ===")

-- 创建模块表（保留模块化结构）
local other = {}

-- 模块自己的变量
other.X = 1000
other.Y = 2000
other.Z = 3000
other.name = "other_global_direct"

print("other模块加载，可以直接使用全局变量 A, B, C")

-- 直接使用全局变量的函数
function other.use_global_vars()
    print("=== other模块直接使用全局变量 ===")
    
    -- 直接使用 A, B, C，无需任何前缀
    print("直接访问 A = " .. A)
    print("直接访问 B = " .. B)
    print("直接访问 C = " .. C)
    
    -- 直接计算
    local sum = A + B + C
    local product = A * B * C
    print("A + B + C = " .. sum)
    print("A * B * C = " .. product)
    
    -- 直接修改全局变量
    print("\nother模块修改全局变量:")
    A = A + 10
    B = B + 20
    C = C + 30
    print("修改后 A = " .. A)
    print("修改后 B = " .. B)
    print("修改后 C = " .. C)
end

-- 与模块自己的变量混合使用
function other.mixed_calculation()
    print("=== 混合使用全局变量和模块变量 ===")
    
    -- 同时使用全局变量 A, B, C 和模块变量 X, Y, Z
    local global_sum = A + B + C
    local module_sum = other.X + other.Y + other.Z
    local total = global_sum + module_sum
    
    print("全局变量总和: " .. global_sum)
    print("模块变量总和: " .. module_sum)
    print("总计: " .. total)
    
    -- 交叉计算
    local cross_calc = A * other.X + B * other.Y + C * other.Z
    print("交叉计算: A*X + B*Y + C*Z = " .. cross_calc)
    
    return total
end

-- 条件逻辑中直接使用全局变量
function other.conditional_logic()
    print("=== other模块中的条件逻辑 ===")
    
    -- 直接在条件中使用全局变量
    if A > B then
        print("A > B，执行相应逻辑")
        C = C + (A - B)
        print("调整 C = " .. C)
    elseif A < B then
        print("A < B，执行相应逻辑")
        C = C + (B - A)
        print("调整 C = " .. C)
    else
        print("A = B，保持平衡")
        C = C + 1
        print("微调 C = " .. C)
    end
    
    -- 复杂条件
    if A + B > 100 then
        print("A + B > 100，需要调整")
        A = A * 0.9
        B = B * 0.9
        print("调整后 A = " .. A .. ", B = " .. B)
    end
end

-- 循环中直接使用全局变量
function other.loop_operations()
    print("=== other模块中的循环操作 ===")
    
    print("执行5次循环操作:")
    for i = 1, 5 do
        -- 直接在循环中使用和修改全局变量
        A = A + i
        B = B + i * 2
        C = C + i * 3
        
        print("第" .. i .. "次: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        
        -- 循环内的条件判断
        if A > 200 then
            print("  A超过200，重置为100")
            A = 100
        end
    end
end

-- 递归函数中使用全局变量
function other.recursive_function(depth)
    if depth <= 0 then
        return A + B + C
    end
    
    print("递归深度 " .. depth .. ": A=" .. A .. ", B=" .. B .. ", C=" .. C)
    
    -- 在递归中修改全局变量
    A = A + 1
    B = B + 2
    C = C + 3
    
    return other.recursive_function(depth - 1)
end

-- 获取贡献值（供main调用）
function other.get_contribution()
    -- 使用全局变量和模块变量计算贡献
    local contribution = (A + B + C) * 0.1 + (other.X + other.Y + other.Z) * 0.05
    print("other模块贡献: " .. contribution)
    return contribution
end

-- 全局变量的批量操作
function other.batch_global_operations()
    print("=== other模块批量操作全局变量 ===")
    
    -- 保存原始值
    local orig_A, orig_B, orig_C = A, B, C
    
    -- 批量修改
    A = A * 1.5
    B = B + 50
    C = C - 10
    
    print("批量修改:")
    print("A: " .. orig_A .. " -> " .. A)
    print("B: " .. orig_B .. " -> " .. B)
    print("C: " .. orig_C .. " -> " .. C)
    
    -- 验证修改
    local new_sum = A + B + C
    local orig_sum = orig_A + orig_B + orig_C
    print("总和变化: " .. orig_sum .. " -> " .. new_sum)
end

-- 与third模块协作（都使用全局变量）
function other.collaborate_with_third()
    print("=== other与third协作（全局变量版本）===")
    
    if _G.third then
        print("协作前全局变量: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        
        -- 调用third模块的函数，third也会直接使用 A, B, C
        if third.use_global_vars then
            third.use_global_vars()
        end
        
        print("协作后全局变量: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        
        -- 联合计算
        local joint_result = A + B + C
        if third.X then
            joint_result = joint_result + third.X
        end
        
        print("联合计算结果: " .. joint_result)
        return joint_result
    else
        print("third模块未找到")
        return A + B + C
    end
end

-- 演示函数：展示所有功能
function other.demo_all_features()
    print("\n=== other模块全功能演示 ===")
    
    print("1. 直接使用全局变量:")
    other.use_global_vars()
    
    print("\n2. 混合计算:")
    other.mixed_calculation()
    
    print("\n3. 条件逻辑:")
    other.conditional_logic()
    
    print("\n4. 循环操作:")
    other.loop_operations()
    
    print("\n5. 递归函数:")
    local recursive_result = other.recursive_function(3)
    print("递归结果: " .. recursive_result)
    
    print("\n6. 批量操作:")
    other.batch_global_operations()
end

-- 打印模块状态
function other.print_status()
    print("=== other模块状态 ===")
    print("全局变量: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    print("模块变量: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
    print("模块名称: " .. other.name)
end

print("other_global_direct.lua 加载完成")

return other
