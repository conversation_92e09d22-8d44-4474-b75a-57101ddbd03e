# Lua测试序列例子

这是一个完整的Lua模块化编程示例，展示了如何优雅地处理模块间的变量访问和函数调用。

## 🎯 设计方案

### 核心理念
- **主脚本**: 使用简单的全局变量 `A`, `B`, `C`
- **other模块**: 使用 `other.X`, `other.Y`, `other.Z`
- **third模块**: 使用 `third.X`, `third.Y`, `third.Z`

### 优势
- ✅ **完全避免命名冲突**
- ✅ **主脚本代码最简洁** (直接用 `A`, `B`, `C`)
- ✅ **变量来源清晰** (通过命名空间识别)
- ✅ **跨模块访问方便** (如 `A + other.X + third.Y`)
- ✅ **性能优秀** (全局变量直接访问)

## 📁 文件结构

```
lua测试序列例子/
├── main.lua        # 主脚本 (使用 A, B, C)
├── other.lua       # other模块 (使用 other.X, Y, Z)
├── third.lua       # third模块 (使用 third.X, Y, Z)
├── run_test.lua    # 完整测试脚本
└── README.md       # 说明文档
```

## 🚀 快速开始

### 运行完整测试
```bash
lua lua测试序列例子/run_test.lua
```

### 单独运行主脚本
```bash
lua lua测试序列例子/main.lua
```

## 💡 使用示例

### 主脚本中的代码 (最简洁)
```lua
-- 设置全局变量
A = 10
B = 20
C = 30

-- 直接使用，无需前缀
local sum = A + B + C
if A > B then
    C = C + 10
end

-- 循环操作
for i = 1, 3 do
    A = A + i
    B = B + i * 2
end
```

### 跨模块访问 (清晰明确)
```lua
-- 访问其他模块的变量
print("other.X = " .. other.X)
print("third.Y = " .. third.Y)

-- 跨模块计算 (来源一目了然)
local result = A + other.X + third.Y
--            ↑     ↑         ↑
--          主脚本  other模块  third模块
```

### 模块内部使用主脚本变量
```lua
-- 在 other.lua 中
function other.use_main_vars()
    -- 直接使用主脚本的全局变量
    print("A = " .. A)  -- 来自主脚本
    print("other.X = " .. other.X)  -- 自己的变量
    
    -- 混合计算
    local result = A + other.X
    
    -- 修改主脚本变量
    A = A + 10
end
```

## 🔧 核心功能

### 主脚本 (main.lua)
- **全局变量**: `A`, `B`, `C`
- **核心函数**: 
  - `main_calculate()` - 基础计算
  - `main_process()` - 数据处理
  - `main_interact_with_modules()` - 模块交互
  - `main_complex_workflow()` - 复杂工作流

### Other模块 (other.lua)
- **模块变量**: `other.X`, `other.Y`, `other.Z`
- **核心函数**:
  - `other.use_main_vars()` - 使用主脚本变量
  - `other.collaborate_with_third()` - 与third模块协作
  - `other.business_logic()` - 业务逻辑处理
  - `other.batch_operations()` - 批量操作

### Third模块 (third.lua)
- **模块变量**: `third.X`, `third.Y`, `third.Z`
- **核心函数**:
  - `third.use_main_vars()` - 使用主脚本变量
  - `third.advanced_math()` - 高级数学运算
  - `third.physics_simulation()` - 物理系统模拟
  - `third.data_analysis()` - 数据分析

## 🎨 设计模式

### 命名空间设计
```lua
A, B, C              # 主脚本全局变量
other.X, other.Y     # other模块变量
third.X, third.Y     # third模块变量
```

### 变量访问模式
```lua
# 主脚本中
A = 10                    # 设置自己的变量
other.X = 1000           # 访问other模块变量
third.Y = 2000           # 访问third模块变量

# other模块中
A = A + 10               # 修改主脚本变量
other.X = other.X + 100  # 修改自己的变量
third.Z = 3000           # 访问third模块变量
```

### 函数调用模式
```lua
# 主脚本调用模块函数
other.use_main_vars()
third.calculate()

# 模块间相互调用
other.collaborate_with_third()
third.collaborate_with_other()
```

## 📊 性能特点

- **全局变量访问**: 最快的访问速度
- **模块表访问**: 通过表索引，性能良好
- **跨模块调用**: 直接函数调用，无额外开销
- **内存占用**: 最小化的内存使用

## 🔍 测试验证

运行 `run_test.lua` 将验证以下功能:

1. ✅ **基础功能**: 变量设置、函数调用
2. ✅ **模块交互**: 跨模块变量访问和修改
3. ✅ **复杂逻辑**: 业务逻辑、数学运算、物理模拟
4. ✅ **批量操作**: 批量修改多模块变量
5. ✅ **命名空间**: 验证无命名冲突
6. ✅ **代码简洁性**: 主脚本代码最简洁

## 🎉 总结

这个示例完美展示了:
- 如何让主脚本使用最简洁的变量名 (`A`, `B`, `C`)
- 如何通过模块化命名避免冲突 (`other.X`, `third.Y`)
- 如何实现清晰的跨模块访问 (`A + other.X + third.Y`)
- 如何保持代码的可读性和维护性

这是一个**优雅、高效、易维护**的Lua模块化编程方案！
