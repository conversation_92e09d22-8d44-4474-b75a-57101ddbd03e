-- 内外部访问方案综合演示

print("=== 内外部访问方案综合演示 ===\n")

-- 先加载third模块作为测试对象
_G.third = dofile("third_global.lua")

print("1. 方案1：局部变量 + 模块表引用")
print("=" * 50)

local other1 = dofile("other_internal_external.lua")

print("\n外部访问测试:")
print("other1.A = " .. other1.A)
print("other1.B = " .. other1.B)

print("\n外部修改测试:")
other1.A = 555
print("设置 other1.A = 555")
print("现在 other1.A = " .. other1.A)

print("\n内外部访问演示:")
other1.demo_internal_external()

print("\n复杂逻辑测试:")
local status1 = other1.run_complex_logic()
print("返回状态: " .. status1)

print("\n批量操作测试:")
other1.batch_operations({
    {type = "add", var = "A", value = 10},
    {type = "multiply", var = "B", value = 1.2},
    {type = "add", var = "C", value = -5}
})

print("\n内部状态检查:")
local state1 = other1.get_internal_state()
print("同步状态: " .. (state1.synchronized and "已同步" or "未同步"))

print("\n\n2. 方案2：元表自动同步")
print("=" * 50)

local other2 = dofile("other_metatable_sync.lua")

print("\n外部访问测试:")
print("other2.A = " .. other2.A)
print("other2.B = " .. other2.B)

print("\n自动同步演示:")
other2.demo_auto_sync()

print("\n复杂逻辑测试:")
local factor2 = other2.run_complex_logic()
print("返回因子: " .. factor2)

print("\n同步状态检查:")
local sync_status2 = other2.get_sync_status()
for key, status in pairs(sync_status2) do
    print(key .. ": 内部=" .. status.internal .. ", 外部=" .. status.external .. ", 同步=" .. (status.synced and "是" or "否"))
end

print("\n\n3. 方案3：环境变量")
print("=" * 50)

local other3 = dofile("other_environment.lua")

print("\n外部访问测试:")
print("other3.A = " .. other3.A)
print("other3.B = " .. other3.B)

print("\n环境演示:")
local result3 = other3.demo_environment()

print("\n批量操作测试:")
other3.batch_operations({
    {type = "set", var = "A", value = 200},
    {type = "add", var = "B", value = 50},
    {type = "multiply", var = "C", value = 0.8}
})

print("\n环境状态检查:")
local env_state3 = other3.get_environment_state()
print("外部A: " .. env_state3.external.A .. ", 内部A: " .. env_state3.internal.A)
print("同步状态: " .. (env_state3.synced and "已同步" or "未同步"))

print("\n\n4. 方案对比测试")
print("=" * 50)

print("\n性能对比（简单访问）:")
local start_time = os.clock()

-- 方案1测试
for i = 1, 1000 do
    local _ = other1.A + other1.B
end
local time1 = os.clock() - start_time

start_time = os.clock()
-- 方案2测试
for i = 1, 1000 do
    local _ = other2.A + other2.B
end
local time2 = os.clock() - start_time

start_time = os.clock()
-- 方案3测试
for i = 1, 1000 do
    local _ = other3.A + other3.B
end
local time3 = os.clock() - start_time

print("方案1 (局部变量+引用): " .. string.format("%.4f", time1) .. "秒")
print("方案2 (元表同步): " .. string.format("%.4f", time2) .. "秒")
print("方案3 (环境变量): " .. string.format("%.4f", time3) .. "秒")

print("\n\n5. 功能对比")
print("=" * 50)

local function test_internal_access(other_module, name)
    print("\n测试 " .. name .. ":")
    
    -- 测试外部访问
    print("  外部访问: other.A = " .. other_module.A)
    
    -- 测试外部修改
    local old_A = other_module.A
    other_module.A = 999
    print("  外部修改: " .. old_A .. " -> " .. other_module.A)
    
    -- 测试内部计算
    local calc_result = other_module.calculate()
    print("  内部计算结果: sum = " .. calc_result.sum)
    
    -- 测试跨模块访问
    local cross_result = other_module.use_third_variables()
    print("  跨模块访问结果: " .. cross_result)
    
    return {
        external_access = true,
        external_modify = true,
        internal_calc = calc_result ~= nil,
        cross_module = cross_result ~= nil
    }
end

local test1 = test_internal_access(other1, "方案1")
local test2 = test_internal_access(other2, "方案2")
local test3 = test_internal_access(other3, "方案3")

print("\n\n6. 总结对比")
print("=" * 50)

print("\n特性对比:")
print("| 特性 | 方案1 | 方案2 | 方案3 |")
print("|------|-------|-------|-------|")
print("| 外部访问 other.A | ✅ | ✅ | ✅ |")
print("| 内部直接用 A | ✅ | ✅ | ✅ |")
print("| 自动同步 | 手动 | 自动 | 手动 |")
print("| 实现复杂度 | 中等 | 高 | 低 |")
print("| 性能 | 好 | 中等 | 好 |")
print("| 内存占用 | 少 | 多 | 少 |")

print("\n推荐使用场景:")
print("• 方案1（局部变量+引用）: 适合需要精确控制同步的场景")
print("• 方案2（元表自动同步）: 适合需要频繁内外部交互的场景")
print("• 方案3（环境变量）: 适合内部逻辑复杂，外部接口简单的场景")

print("\n最终推荐:")
print("对于您的需求（外部 other.xxxvar，内部直接 xxxvar），")
print("推荐使用 方案3（环境变量），因为:")
print("✅ 实现最简洁")
print("✅ 内部代码最清晰")
print("✅ 性能良好")
print("✅ 易于维护")

print("\n演示完成！")
