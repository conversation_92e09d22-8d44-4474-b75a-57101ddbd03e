-- 测试共享变量的各种方法

print("=== 测试共享变量系统 ===\n")

-- 方法1：直接测试全局变量共享
print("1. 测试直接全局变量共享:")
print("执行 main_script.lua...")
dofile("main_script.lua")

print("\n执行 other_script.lua...")
local other_result = dofile("other_script.lua")

print("\n在 other_script 中修改变量后，检查主脚本中的变量:")
print("主脚本中的 A = " .. A)
print("主脚本中的 B = " .. B)
print("主脚本中的 C = " .. C)

-- 方法2：测试纯Lua元方法实现
print("\n\n2. 测试纯Lua元方法实现:")

-- 重新加载共享变量系统
package.loaded["shared_vars"] = nil
local SharedVars = require("shared_vars")

-- 清除之前的变量
A, B, C = nil, nil, nil

-- 初始化系统
SharedVars.init()

-- 设置一些测试变量
print("设置测试变量...")
X = 100  -- 这会触发 __newindex
Y = 200
Z = 300

-- 创建一个新的环境来测试变量访问
local test_env = {}
setmetatable(test_env, {__index = _G})

-- 在新环境中访问变量
print("\n在新环境中访问变量:")
local function test_in_env()
    print("X = " .. X)  -- 这会触发 __index
    print("Y = " .. Y)
    print("Z = " .. Z)
    
    -- 修改变量
    X = X + 10
    print("修改后 X = " .. X)
end

-- 设置环境并执行
setfenv(test_in_env, test_env)
test_in_env()

-- 检查原始环境中的变量
print("\n检查原始环境中的变量:")
print("原始 X = " .. _G.X)

-- 打印所有共享变量
print("\n")
SharedVars.print_all()

-- 方法3：演示如何在不同脚本间传递变量
print("\n\n3. 演示脚本间变量传递:")

-- 创建一个临时脚本文件
local temp_script = [[
-- 临时脚本
print("临时脚本开始执行")
print("访问共享变量 X = " .. (X or "nil"))
print("访问共享变量 Y = " .. (Y or "nil"))

-- 设置新变量
TEMP_VAR = 999
print("设置 TEMP_VAR = " .. TEMP_VAR)

return {message = "临时脚本执行完成", temp_value = TEMP_VAR}
]]

-- 写入临时文件
local file = io.open("temp_test.lua", "w")
file:write(temp_script)
file:close()

-- 执行临时脚本
print("执行临时脚本...")
local temp_result = dofile("temp_test.lua")
print("临时脚本返回:", temp_result.message)

-- 检查新变量是否可访问
print("检查 TEMP_VAR = " .. (TEMP_VAR or "nil"))

-- 清理临时文件
os.remove("temp_test.lua")

print("\n=== 测试完成 ===")
