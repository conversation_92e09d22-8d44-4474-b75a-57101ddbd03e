# Makefile for Lua shared variables extension

# 编译器设置
CXX = g++
CXXFLAGS = -std=c++11 -fPIC -O2 -Wall

# Lua相关路径（根据您的系统调整）
LUA_INCLUDE = -I/usr/include/lua5.3
LUA_LIB = -llua5.3

# Windows下的设置（如果在Windows上编译）
ifeq ($(OS),Windows_NT)
    LUA_INCLUDE = -I"C:/Program Files/Lua/include"
    LUA_LIB = -L"C:/Program Files/Lua/lib" -llua53
    SHARED_EXT = .dll
    SHARED_FLAGS = -shared
else
    SHARED_EXT = .so
    SHARED_FLAGS = -shared
endif

# 目标文件
TARGET = shared_vars$(SHARED_EXT)
SOURCE = lua_shared_vars.cpp

# 默认目标
all: $(TARGET)

# 编译共享库
$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) $(SHARED_FLAGS) $(LUA_INCLUDE) -o $@ $< $(LUA_LIB)

# 清理
clean:
	rm -f $(TARGET)

# 测试
test: $(TARGET)
	lua test_shared_vars.lua

.PHONY: all clean test
