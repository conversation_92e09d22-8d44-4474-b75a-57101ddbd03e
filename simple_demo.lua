-- 简单演示变量共享

print("=== Lua变量共享演示 ===\n")

-- 方法1：直接全局变量共享
print("1. 直接全局变量共享演示:")

-- 模拟 main_script.lua 的行为
print("在主脚本中设置变量:")
A = 10
B = 20
C = 30
print("A = " .. A)
print("B = " .. B)
print("C = " .. C)

-- 模拟 other_script.lua 的行为
print("\n在其他脚本中访问变量:")
print("读取到的 A = " .. A)
print("读取到的 B = " .. B)
print("读取到的 C = " .. C)

-- 在其他脚本中修改变量
print("\n在其他脚本中修改变量:")
A = A + 100
B = B + 200
C = C + 300
print("修改后 A = " .. A)
print("修改后 B = " .. B)
print("修改后 C = " .. C)

-- 方法2：使用表来模拟共享存储
print("\n\n2. 使用表模拟共享存储:")

-- 创建共享存储
local shared_storage = {}

-- 模拟 __newindex 行为
local function set_shared_var(key, value)
    shared_storage[key] = value
    _G[key] = value
    print("设置共享变量: " .. key .. " = " .. value)
end

-- 模拟 __index 行为
local function get_shared_var(key)
    if shared_storage[key] ~= nil then
        print("从共享存储读取: " .. key .. " = " .. shared_storage[key])
        return shared_storage[key]
    else
        return _G[key]
    end
end

-- 测试共享变量
print("设置共享变量:")
set_shared_var("X", 100)
set_shared_var("Y", 200)
set_shared_var("Z", 300)

print("\n读取共享变量:")
local x = get_shared_var("X")
local y = get_shared_var("Y")
local z = get_shared_var("Z")

print("计算结果: X + Y + Z = " .. (x + y + z))

-- 方法3：演示环境隔离
print("\n\n3. 演示环境隔离和共享:")

-- 创建新环境
local env1 = {
    A = 1000,
    B = 2000,
    print = print,  -- 需要显式提供print函数
    tostring = tostring
}

local env2 = {
    A = 5000,
    B = 6000,
    print = print,
    tostring = tostring
}

-- 在不同环境中执行相同代码
local function test_function()
    print("环境中的 A = " .. tostring(A))
    print("环境中的 B = " .. tostring(B))
    print("A + B = " .. tostring(A + B))
end

print("在环境1中执行:")
setfenv(test_function, env1)
test_function()

print("\n在环境2中执行:")
setfenv(test_function, env2)
test_function()

print("\n在全局环境中执行:")
setfenv(test_function, _G)
test_function()

print("\n=== 演示完成 ===")

-- 总结
print("\n总结:")
print("1. 全局变量在所有脚本间自动共享")
print("2. 可以通过表和函数模拟元方法行为")
print("3. 环境(environment)可以控制变量的作用域")
print("4. C++扩展可以提供更强大的变量管理功能")
