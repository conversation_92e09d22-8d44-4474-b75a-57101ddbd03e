-- 主脚本 - 全局变量版本
-- A、B、C 设置为全局变量，任何地方都可以直接使用

print("=== 主脚本全局变量版本 ===\n")

-- 方案1：直接设置全局变量
A = 10
B = 20
C = 30
main_name = "main_global_vars"

print("设置了全局变量:")
print("A = " .. A)
print("B = " .. B)
print("C = " .. C)

-- 验证全局变量
print("\n验证全局变量:")
print("_G.A = " .. _G.A)
print("_G.B = " .. _G.B)
print("_G.C = " .. _G.C)

-- 加载其他模块
print("\n加载其他模块...")
local other = dofile("other_global_direct.lua")
local third = dofile("third_global_direct.lua")

-- 在main中直接使用全局变量
print("\n在main中直接使用全局变量:")

-- 直接计算（无需前缀）
local sum = A + B + C
local product = A * B * C
print("A + B + C = " .. sum)
print("A * B * C = " .. product)

-- 直接修改（无需前缀）
print("\n在main中修改全局变量:")
local old_A = A
A = A + 100
print("A: " .. old_A .. " -> " .. A)

local old_B = B
B = B * 2
print("B: " .. old_B .. " -> " .. B)

-- 条件逻辑（直接使用变量）
print("\n条件逻辑演示:")
if A > 50 then
    C = C + 10
    print("因为 A > 50，所以 C 增加到 " .. C)
end

if B > 30 then
    A = A - 5
    print("因为 B > 30，所以 A 减少到 " .. A)
end

-- 循环操作（直接使用变量）
print("\n循环操作演示:")
for i = 1, 3 do
    A = A + i
    B = B + i * 2
    C = C + i * 3
    print("第" .. i .. "次: A=" .. A .. ", B=" .. B .. ", C=" .. C)
end

-- 函数中直接使用全局变量
function main_calculate()
    print("\nmain_calculate函数中直接使用全局变量:")
    
    -- 直接使用 A, B, C，无需任何前缀
    local result = A * 2 + B * 3 + C * 4
    print("A*2 + B*3 + C*4 = " .. result)
    
    -- 直接修改
    A = A + 1
    B = B + 2
    C = C + 3
    print("修改后: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    
    return result
end

local calc_result = main_calculate()

-- 复杂逻辑函数
function main_complex_logic()
    print("\n复杂逻辑函数:")
    
    -- 直接使用变量进行复杂计算
    local factor = (A + B) / C
    print("因子 (A+B)/C = " .. factor)
    
    -- 根据因子调整变量
    if factor > 5 then
        A = A * 0.8
        B = B * 0.8
        C = C * 1.2
        print("因子过大，调整变量")
    elseif factor < 2 then
        A = A * 1.2
        B = B * 1.2
        C = C * 0.8
        print("因子过小，调整变量")
    end
    
    print("调整后: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    return factor
end

local factor_result = main_complex_logic()

-- 与其他模块交互
print("\n与其他模块交互:")

-- 调用other模块的函数，other模块内部也可以直接使用 A, B, C
if other and other.use_global_vars then
    print("调用other模块使用全局变量:")
    other.use_global_vars()
end

-- 调用third模块的函数
if third and third.use_global_vars then
    print("调用third模块使用全局变量:")
    third.use_global_vars()
end

-- 跨模块联合计算
print("\n跨模块联合计算:")
local main_contribution = A + B + C
local other_contribution = 0
local third_contribution = 0

if other and other.get_contribution then
    other_contribution = other.get_contribution()
end

if third and third.get_contribution then
    third_contribution = third.get_contribution()
end

local total_contribution = main_contribution + other_contribution + third_contribution
print("main贡献: " .. main_contribution)
print("other贡献: " .. other_contribution)
print("third贡献: " .. third_contribution)
print("总贡献: " .. total_contribution)

-- 全局变量状态监控
function print_global_state()
    print("\n=== 全局变量状态 ===")
    print("A = " .. A)
    print("B = " .. B)
    print("C = " .. C)
    print("总和 = " .. (A + B + C))
    print("乘积 = " .. (A * B * C))
end

print_global_state()

-- 批量操作全局变量
function batch_modify_globals(operations)
    print("\n批量修改全局变量:")
    
    for i, op in ipairs(operations) do
        if op.var == "A" then
            if op.type == "add" then A = A + op.value
            elseif op.type == "multiply" then A = A * op.value
            elseif op.type == "set" then A = op.value
            end
        elseif op.var == "B" then
            if op.type == "add" then B = B + op.value
            elseif op.type == "multiply" then B = B * op.value
            elseif op.type == "set" then B = op.value
            end
        elseif op.var == "C" then
            if op.type == "add" then C = C + op.value
            elseif op.type == "multiply" then C = C * op.value
            elseif op.type == "set" then C = op.value
            end
        end
        
        print("操作" .. i .. ": " .. op.type .. " " .. op.var .. " " .. op.value)
    end
    
    print("批量操作完成")
    print_global_state()
end

-- 执行批量操作
batch_modify_globals({
    {type = "add", var = "A", value = 5},
    {type = "multiply", var = "B", value = 1.1},
    {type = "set", var = "C", value = 100}
})

-- 最终状态
print("\n=== 最终状态 ===")
print("主脚本执行完成")
print("全局变量可以在任何地方直接使用:")
print("• 在main中: 直接用 A, B, C")
print("• 在other中: 直接用 A, B, C")
print("• 在third中: 直接用 A, B, C")
print("• 在任何函数中: 直接用 A, B, C")

print_global_state()

print("\n优势:")
print("✅ 无需前缀，直接使用变量名")
print("✅ 代码最简洁")
print("✅ 任何地方都可以访问和修改")
print("✅ 性能最好（直接全局变量访问）")

print("\n注意事项:")
print("⚠️ 需要避免变量名冲突")
print("⚠️ 建议使用有意义的变量名")
print("⚠️ 可以考虑添加前缀避免冲突（如 MAIN_A, MAIN_B）")
