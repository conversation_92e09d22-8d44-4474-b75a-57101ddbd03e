@echo off
echo ===================================
echo Lua Portable 安装脚本
echo ===================================

set INSTALL_DIR=%~dp0lua-portable
set LUA_VERSION=5.4.4

echo.
echo 🎯 安装目标: %INSTALL_DIR%
echo 📦 Lua 版本: %LUA_VERSION%
echo.

:: 创建安装目录
if not exist "%INSTALL_DIR%" (
    echo 📁 创建安装目录...
    mkdir "%INSTALL_DIR%"
    mkdir "%INSTALL_DIR%\bin"
    mkdir "%INSTALL_DIR%\lib"
    mkdir "%INSTALL_DIR%\include"
    mkdir "%INSTALL_DIR%\scripts"
)

:: 检查是否已安装
if exist "%INSTALL_DIR%\bin\lua.exe" (
    echo ✅ Lua 已安装在 %INSTALL_DIR%
    goto :test_installation
)

echo 📥 下载 Lua %LUA_VERSION% ...

:: 使用 PowerShell 下载
powershell -Command "& {
    $url = 'https://sourceforge.net/projects/luabinaries/files/%LUA_VERSION%/Tools%%20Executables/lua-%LUA_VERSION%_Win64_bin.zip/download'
    $output = '%INSTALL_DIR%\lua-bin.zip'
    Write-Host '正在下载...'
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -UserAgent 'Mozilla/5.0'
        Write-Host '✅ 下载完成'
    } catch {
        Write-Host '❌ 下载失败，请手动下载'
        Write-Host '下载地址: http://luabinaries.sourceforge.net/'
        exit 1
    }
}"

if not exist "%INSTALL_DIR%\lua-bin.zip" (
    echo ❌ 下载失败，请手动下载 Lua 二进制文件
    echo 📍 下载地址: http://luabinaries.sourceforge.net/
    echo 📍 下载文件: lua-%LUA_VERSION%_Win64_bin.zip
    echo 📍 解压到: %INSTALL_DIR%\bin\
    pause
    goto :manual_setup
)

echo 📦 解压文件...
powershell -Command "Expand-Archive -Path '%INSTALL_DIR%\lua-bin.zip' -DestinationPath '%INSTALL_DIR%\temp' -Force"

:: 移动文件到正确位置
if exist "%INSTALL_DIR%\temp\lua54.exe" (
    move "%INSTALL_DIR%\temp\lua54.exe" "%INSTALL_DIR%\bin\lua.exe"
)
if exist "%INSTALL_DIR%\temp\luac54.exe" (
    move "%INSTALL_DIR%\temp\luac54.exe" "%INSTALL_DIR%\bin\luac.exe"
)
if exist "%INSTALL_DIR%\temp\lua54.dll" (
    move "%INSTALL_DIR%\temp\lua54.dll" "%INSTALL_DIR%\bin\lua54.dll"
)

:: 清理临时文件
if exist "%INSTALL_DIR%\temp" rmdir /s /q "%INSTALL_DIR%\temp"
if exist "%INSTALL_DIR%\lua-bin.zip" del "%INSTALL_DIR%\lua-bin.zip"

:manual_setup
:: 创建启动脚本
echo 📝 创建启动脚本...

:: 创建 lua.bat
echo @echo off > "%INSTALL_DIR%\lua.bat"
echo set LUA_PATH=%%~dp0scripts\?.lua;%%~dp0scripts\?\init.lua;%%LUA_PATH%% >> "%INSTALL_DIR%\lua.bat"
echo set LUA_CPATH=%%~dp0lib\?.dll;%%LUA_CPATH%% >> "%INSTALL_DIR%\lua.bat"
echo "%%~dp0bin\lua.exe" %%* >> "%INSTALL_DIR%\lua.bat"

:: 创建 luac.bat
echo @echo off > "%INSTALL_DIR%\luac.bat"
echo "%%~dp0bin\luac.exe" %%* >> "%INSTALL_DIR%\luac.bat"

:: 创建环境设置脚本
echo 📝 创建环境设置脚本...
echo @echo off > "%INSTALL_DIR%\set_lua_env.bat"
echo echo 设置 Lua Portable 环境... >> "%INSTALL_DIR%\set_lua_env.bat"
echo set PATH=%%~dp0;%%~dp0bin;%%PATH%% >> "%INSTALL_DIR%\set_lua_env.bat"
echo set LUA_PATH=%%~dp0scripts\?.lua;%%~dp0scripts\?\init.lua;%%LUA_PATH%% >> "%INSTALL_DIR%\set_lua_env.bat"
echo set LUA_CPATH=%%~dp0lib\?.dll;%%LUA_CPATH%% >> "%INSTALL_DIR%\set_lua_env.bat"
echo echo ✅ Lua Portable 环境已设置 >> "%INSTALL_DIR%\set_lua_env.bat"
echo echo 💡 使用方法: >> "%INSTALL_DIR%\set_lua_env.bat"
echo echo    lua script.lua >> "%INSTALL_DIR%\set_lua_env.bat"
echo echo    luac script.lua >> "%INSTALL_DIR%\set_lua_env.bat"
echo cmd /k >> "%INSTALL_DIR%\set_lua_env.bat"

:: 创建测试脚本
echo 📝 创建测试脚本...
echo print("🎉 Lua Portable 安装成功!") > "%INSTALL_DIR%\scripts\test.lua"
echo print("Lua 版本: " .. _VERSION) >> "%INSTALL_DIR%\scripts\test.lua"
echo print("当前目录: " .. os.getenv("PWD") or "N/A") >> "%INSTALL_DIR%\scripts\test.lua"
echo print("LUA_PATH: " .. (package.path or "N/A")) >> "%INSTALL_DIR%\scripts\test.lua"

:: 创建使用说明
echo 📝 创建使用说明...
echo Lua Portable 使用说明 > "%INSTALL_DIR%\README.txt"
echo ======================= >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo 🚀 快速开始: >> "%INSTALL_DIR%\README.txt"
echo 1. 双击 set_lua_env.bat 设置环境 >> "%INSTALL_DIR%\README.txt"
echo 2. 在命令行中输入: lua scripts\test.lua >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo 📁 目录结构: >> "%INSTALL_DIR%\README.txt"
echo bin\        - Lua 可执行文件 >> "%INSTALL_DIR%\README.txt"
echo lib\        - Lua 库文件 >> "%INSTALL_DIR%\README.txt"
echo scripts\    - Lua 脚本文件 >> "%INSTALL_DIR%\README.txt"
echo. >> "%INSTALL_DIR%\README.txt"
echo 💡 使用方法: >> "%INSTALL_DIR%\README.txt"
echo lua.bat script.lua     - 运行脚本 >> "%INSTALL_DIR%\README.txt"
echo luac.bat script.lua    - 编译脚本 >> "%INSTALL_DIR%\README.txt"

:test_installation
:: 测试安装
echo.
echo 🧪 测试安装...
if exist "%INSTALL_DIR%\bin\lua.exe" (
    echo ✅ lua.exe 存在
    "%INSTALL_DIR%\bin\lua.exe" -v 2>nul
    if errorlevel 1 (
        echo ⚠️  lua.exe 可能需要依赖库
    ) else (
        echo ✅ lua.exe 运行正常
    )
) else (
    echo ❌ lua.exe 不存在，请手动安装
)

echo.
echo 🎉 Lua Portable 安装完成!
echo.
echo 📍 安装位置: %INSTALL_DIR%
echo 🚀 启动方式: 双击 %INSTALL_DIR%\set_lua_env.bat
echo 🧪 测试命令: lua scripts\test.lua
echo 📖 使用说明: 查看 %INSTALL_DIR%\README.txt
echo.

:: 询问是否立即测试
set /p choice="是否立即测试 Lua? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🧪 运行测试...
    "%INSTALL_DIR%\bin\lua.exe" "%INSTALL_DIR%\scripts\test.lua"
    echo.
)

echo 按任意键退出...
pause >nul
