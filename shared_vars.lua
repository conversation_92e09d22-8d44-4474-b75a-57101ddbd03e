-- 纯Lua实现的共享变量系统
-- 使用元表来实现变量共享

local SharedVars = {}

-- 共享变量存储
local shared_storage = {}

-- 原始的全局环境
local original_G = _G

-- __index 元方法：当访问不存在的变量时调用
function SharedVars.__index(t, key)
    -- 首先检查共享存储
    if shared_storage[key] ~= nil then
        print("从共享存储读取变量: " .. key .. " = " .. tostring(shared_storage[key]))
        return shared_storage[key]
    end
    
    -- 然后检查原始全局环境
    return original_G[key]
end

-- __newindex 元方法：当设置新变量时调用
function SharedVars.__newindex(t, key, value)
    print("设置共享变量: " .. key .. " = " .. tostring(value))
    
    -- 存储到共享存储中
    shared_storage[key] = value
    
    -- 同时存储到原始全局环境中
    original_G[key] = value
end

-- 初始化共享变量系统
function SharedVars.init()
    -- 设置全局环境的元表
    setmetatable(_G, SharedVars)
    print("共享变量系统已初始化")
end

-- 手动设置共享变量
function SharedVars.set(key, value)
    shared_storage[key] = value
    _G[key] = value
    print("手动设置共享变量: " .. key .. " = " .. tostring(value))
end

-- 获取共享变量
function SharedVars.get(key)
    return shared_storage[key]
end

-- 打印所有共享变量
function SharedVars.print_all()
    print("=== 所有共享变量 ===")
    for key, value in pairs(shared_storage) do
        print(key .. " = " .. tostring(value))
    end
end

-- 获取共享存储的副本
function SharedVars.get_storage()
    local copy = {}
    for k, v in pairs(shared_storage) do
        copy[k] = v
    end
    return copy
end

-- 清除所有共享变量
function SharedVars.clear()
    for key in pairs(shared_storage) do
        shared_storage[key] = nil
        _G[key] = nil
    end
    print("已清除所有共享变量")
end

-- 从另一个环境导入变量
function SharedVars.import_from_env(env, keys)
    for _, key in ipairs(keys) do
        if env[key] ~= nil then
            SharedVars.set(key, env[key])
        end
    end
end

return SharedVars
