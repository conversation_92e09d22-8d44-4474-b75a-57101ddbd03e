-- other模块 - 内外部访问分离版本
-- 外部：other.xxxvar  内部：直接 xxxvar

print("=== other_internal_external.lua 开始加载 ===")

-- 创建模块表
local other = {}

-- 方案1：局部变量 + 模块表引用
local A = 100
local B = 200
local C = 300
local name = "other_internal_external"
local version = "4.0"

-- 将局部变量映射到模块表（供外部访问）
other.A = A
other.B = B
other.C = C
other.name = name
other.version = version

print("设置了局部变量和模块表映射")

-- 内部函数：直接使用局部变量
local function internal_calculate()
    -- 内部直接使用 A, B, C
    local sum = A + B + C
    local product = A * B * C
    local average = sum / 3
    
    print("内部计算（使用局部变量）:")
    print("  A + B + C = " .. sum)
    print("  A * B * C = " .. product)
    print("  平均值 = " .. average)
    
    return {sum = sum, product = product, average = average}
end

-- 内部函数：修改变量
local function internal_modify_vars(new_A, new_B, new_C)
    print("内部修改变量:")
    
    if new_A then
        A = new_A
        other.A = A  -- 同步到模块表
        print("  内部设置 A = " .. A)
    end
    
    if new_B then
        B = new_B
        other.B = B  -- 同步到模块表
        print("  内部设置 B = " .. B)
    end
    
    if new_C then
        C = new_C
        other.C = C  -- 同步到模块表
        print("  内部设置 C = " .. C)
    end
end

-- 内部函数：打印变量
local function internal_print_vars()
    print("=== 内部变量状态 ===")
    print("内部 A = " .. A)
    print("内部 B = " .. B)
    print("内部 C = " .. C)
    print("内部 name = " .. name)
end

-- 内部函数：使用其他模块
local function internal_use_third()
    print("=== 内部访问third模块 ===")
    
    if _G.third then
        print("内部访问 third.X = " .. third.X)
        
        -- 内部计算：使用局部变量和third模块
        local combined = A + B + third.X
        print("内部计算 A + B + third.X = " .. combined)
        
        -- 内部修改third模块
        third.Y = third.Y + A
        print("内部修改 third.Y = " .. third.Y)
        
        return combined
    else
        print("third模块未找到")
        return 0
    end
end

-- 外部接口函数：供外部调用
function other.print_variables()
    print("=== other模块变量（外部接口）===")
    print("other.A = " .. other.A)
    print("other.B = " .. other.B)
    print("other.C = " .. other.C)
    print("other.name = " .. other.name)
    
    -- 同时显示内部状态
    print("\n内部状态:")
    internal_print_vars()
end

function other.calculate()
    print("外部调用计算函数")
    return internal_calculate()
end

function other.set_variables(new_A, new_B, new_C)
    print("外部设置变量:")
    internal_modify_vars(new_A, new_B, new_C)
end

function other.use_third_variables()
    print("外部调用访问third模块")
    return internal_use_third()
end

-- 演示内外部访问差异
function other.demo_internal_external()
    print("\n=== 演示内外部访问差异 ===")
    
    print("1. 内部访问（直接使用变量名）:")
    print("   A = " .. A)
    print("   B = " .. B)
    print("   C = " .. C)
    
    print("\n2. 外部访问（通过模块表）:")
    print("   other.A = " .. other.A)
    print("   other.B = " .. other.B)
    print("   other.C = " .. other.C)
    
    print("\n3. 内部修改测试:")
    local old_A = A
    A = A + 100  -- 内部直接修改
    other.A = A  -- 同步到外部
    print("   内部修改 A: " .. old_A .. " -> " .. A)
    print("   外部看到的 other.A = " .. other.A)
    
    print("\n4. 内部计算测试:")
    local result = A * B + C  -- 内部直接计算
    print("   内部计算 A * B + C = " .. result)
    
    return result
end

-- 复杂的内部逻辑示例
local function complex_internal_logic()
    print("=== 复杂内部逻辑 ===")
    
    -- 内部可以直接使用变量，代码更简洁
    if A > 100 then
        B = B * 1.1
        C = C + A * 0.1
    elseif A < 50 then
        B = B * 0.9
        C = C - 10
    end
    
    -- 内部条件判断
    local status = "normal"
    if A + B + C > 1000 then
        status = "high"
        A = A * 0.95  -- 自动调节
    elseif A + B + C < 100 then
        status = "low"
        A = A * 1.05  -- 自动调节
    end
    
    -- 同步到外部
    other.A = A
    other.B = B
    other.C = C
    
    print("内部逻辑处理完成，状态: " .. status)
    return status
end

function other.run_complex_logic()
    print("外部调用复杂逻辑")
    return complex_internal_logic()
end

-- 内部工具函数
local function validate_values()
    return A > 0 and B > 0 and C > 0
end

local function normalize_values()
    if not validate_values() then
        A = math.max(A, 1)
        B = math.max(B, 1)
        C = math.max(C, 1)
        
        -- 同步到外部
        other.A = A
        other.B = B
        other.C = C
        
        print("内部标准化了负值")
    end
end

function other.normalize()
    print("外部调用标准化")
    normalize_values()
end

-- 批量内部操作
local function batch_internal_operations(operations)
    print("执行批量内部操作:")
    
    for i, op in ipairs(operations) do
        if op.type == "add" then
            if op.var == "A" then A = A + op.value
            elseif op.var == "B" then B = B + op.value
            elseif op.var == "C" then C = C + op.value
            end
        elseif op.type == "multiply" then
            if op.var == "A" then A = A * op.value
            elseif op.var == "B" then B = B * op.value
            elseif op.var == "C" then C = C * op.value
            end
        end
        
        print("  操作 " .. i .. ": " .. op.type .. " " .. op.var .. " " .. op.value)
    end
    
    -- 同步所有变量到外部
    other.A = A
    other.B = B
    other.C = C
    
    print("批量操作完成，已同步到外部")
end

function other.batch_operations(operations)
    return batch_internal_operations(operations)
end

-- 获取内部状态
function other.get_internal_state()
    return {
        internal_A = A,
        internal_B = B,
        internal_C = C,
        external_A = other.A,
        external_B = other.B,
        external_C = other.C,
        synchronized = (A == other.A and B == other.B and C == other.C)
    }
end

print("other_internal_external.lua 加载完成")

return other
