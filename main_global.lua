-- 主脚本 - 全局模块管理版本
-- 在main中统一导入所有模块，其他模块无需单独导入

print("=== 全局模块管理演示 ===\n")

-- 1. 加载全局模块管理器
local GlobalModuleManager = require("global_module_manager")

-- 2. 在main中统一导入所有模块
print("1. 统一导入所有模块:")

-- 方法1：批量导入
local module_configs = {
    {name = "other", path = "other_global.lua"},
    {name = "third", path = "third_global.lua"}
}

GlobalModuleManager.batch_import(module_configs)

-- 方法2：手动导入（备选方案）
-- local other = dofile("other_global.lua")
-- local third = dofile("third_global.lua")
-- GlobalModuleManager.register("other", other)
-- GlobalModuleManager.register("third", third)
-- GlobalModuleManager.enable_cross_access()

-- 3. 设置模块间通信
GlobalModuleManager.setup_communication()

-- 4. 创建全局访问代理
local modules = GlobalModuleManager.create_global_proxy()

print("\n2. 验证模块已全局可用:")
GlobalModuleManager.list()

-- 5. 演示：在main中使用 other.xxxvar 形式访问
print("\n3. 在main中访问模块变量:")
print("other.A = " .. other.A)
print("other.B = " .. other.B)
print("third.X = " .. third.X)
print("third.Y = " .. third.Y)

-- 6. 演示：在main中修改模块变量
print("\n4. 在main中修改模块变量:")
other.A = 888
third.X = 9999
print("修改后 other.A = " .. other.A)
print("修改后 third.X = " .. third.X)

-- 7. 演示：模块间直接访问（无需导入）
print("\n5. 模块间直接访问演示:")
print("调用other模块访问third模块的函数:")
other.use_third_variables()

print("\n调用third模块访问other模块的函数:")
third.use_other_variables()

-- 8. 演示：复杂的跨模块操作
print("\n6. 复杂跨模块操作:")
local result1 = other.collaborate_with_third()
local result2 = third.complex_interaction()

print("协作结果1: " .. result1)
print("协作结果2: " .. result2)

-- 9. 演示：事件通信
print("\n7. 事件通信演示:")
print("发送全局事件...")

-- 发送一个全局事件
emit_event("global_test", {message = "来自main的全局测试事件"}, "main")

-- 让模块发送事件给彼此
other.set_A(1500)  -- 这会触发事件通知
third.request_calculation_from_others()

-- 10. 演示：批量操作
print("\n8. 批量操作演示:")
third.batch_operations()

-- 11. 演示：通过全局代理访问
print("\n9. 通过全局代理访问:")
print("modules.other.A = " .. modules.other.A)
print("modules.third.X = " .. modules.third.X)

-- 可以动态添加新模块
modules.new_module = {
    test_var = 12345,
    test_func = function() print("新模块函数被调用") end
}

print("动态添加的模块: modules.new_module.test_var = " .. modules.new_module.test_var)

-- 12. 演示：复杂的联合计算
print("\n10. 复杂联合计算:")
local function complex_calculation()
    -- 使用所有模块的变量进行计算
    local total = 0
    
    -- 从other模块
    total = total + other.A + other.B + other.C
    
    -- 从third模块
    total = total + third.X + third.Y + third.Z
    
    -- 从新模块
    total = total + modules.new_module.test_var
    
    print("所有模块变量总和: " .. total)
    
    -- 根据结果调整所有模块
    if total > 20000 then
        print("总和超过20000，调整所有模块变量")
        other.A = other.A * 0.9
        third.X = third.X * 0.9
        modules.new_module.test_var = modules.new_module.test_var * 0.9
    end
    
    return total
end

local final_result = complex_calculation()

-- 13. 最终状态展示
print("\n11. 最终状态:")
print("=== 最终模块状态 ===")
other.print_variables()
third.print_variables()
print("new_module.test_var = " .. modules.new_module.test_var)

-- 14. 演示模块的全局访问功能
print("\n12. 模块自主演示:")
other.demo_global_access()
third.demo_global_access()

-- 15. 总结
print("\n=== 总结 ===")
print("✅ 在main中统一导入了所有模块")
print("✅ 模块间可以直接访问，无需单独导入")
print("✅ 支持 other.xxxvar 形式的访问")
print("✅ 支持跨模块的变量读写")
print("✅ 支持模块间事件通信")
print("✅ 支持动态模块管理")
print("✅ 所有操作在同一个Lua环境中进行")

print("\n关键优势:")
print("1. 统一管理：所有模块在main中统一导入和管理")
print("2. 全局可用：任何模块都可以访问其他模块，无需单独导入")
print("3. 透明访问：使用简单的 module.variable 语法")
print("4. 事件通信：模块间可以通过事件进行通信")
print("5. 动态扩展：可以在运行时添加新模块")

print("\n最终计算结果: " .. final_result)
