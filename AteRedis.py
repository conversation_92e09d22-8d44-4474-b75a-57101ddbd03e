import redis
import os
from datetime import datetime
import threading
import json
import traceback
from ATE_WriteLog import __writeToLog as __writeToLog
from time import sleep


ConFileName='AteRedis'
conWriteerr=11  #写异常
conReadErr=22   #读异常
conFuncErr=33  #抛出异常


# 配置 Redis 连接（根据实际情况修改）
REDIS_CONFIG = {
    'host': 'localhost',
    'port': 6379,
    'db': 0,
    'password': None,  # 如果没有密码保持 None
    'decode_responses': True  # 自动解码二进制值为字符串
}

class RedisManger:

    def get_redis_connection(self,seq_context):
        try:
            """创建 Redis 连接池"""
            pool = redis.ConnectionPool(**REDIS_CONFIG)
            return redis.Redis(connection_pool=pool)
        except Exception as e:
            errorStr = str(e)
    def __del__(self):
        """析构函数自动释放连接"""
        self.disconnect()

# 创建Redis连接
def RedisConnect():
    redisSession = redis.Redis(host='localhost', port=6379, db=0)
    if redisSession:
        return redisSession
    else:
        return None

def RedisClose(redisSession):
    try:
        redisSession.close()
        print("redis关闭成功")
    except Exception as e:
        print(e)

def get_redis_connection(seq_context):
    try:
        """创建 Redis 连接池"""
        pool = redis.ConnectionPool(**REDIS_CONFIG)
        return redis.Redis(connection_pool=pool)
    except Exception as e:
        errorStr = str(e)
        __writeToLog('get_redis_connection error' + errorStr)


def redis_disconnection(r,seq_context):
    try:
        """关闭连接"""
        r.close()
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__redis_disconnection error' + errorStr)

def redis_Dev_Subscribe(deviceAliase,retRedis,seq_context):
    try:
        idevName = deviceAliase
        channel = ["device:ack:" + idevName, "device:response:" + idevName]
        rr = __subscribe_channel(retRedis, channel)  # 订阅
        return rr
    except Exception as e:
        errorStr = str(e)
        __writeToLog('redis_Dev_subscribe error' + errorStr)
        return None

def redis_Dev_UnSubscribe(deviceAliase,retRedisSub,seq_context):
    try:
        idevName = deviceAliase
        channel = ["device:ack:" + idevName, "device:response:" + idevName]
        __Unsubscribe_channel(retRedisSub, channel)  # 订阅
        return rr
    except Exception as e:
        errorStr = str(e)
        __writeToLog('redis_Dev_subscribe error' + errorStr)
        return None

def get_value(r, key):
    """根据数据类型获取值"""
    key_type = r.type(key).decode('utf-8') if isinstance(r.type(key), bytes) else r.type(key)

    try:
        if key_type == 'string':
            return r.get(key)
        elif key_type == 'hash':
            #return r.hgetall(key)
            print(key)
            if key!='ATE':
                data=r.hgetall(key)
            else:
                data=''
            return data
        elif key_type == 'list':
            return r.lrange(key, 0, -1)
        elif key_type == 'set':
            return r.smembers(key)
        elif key_type == 'zset':
            return r.zrange(key, 0, -1, withscores=True)
        else:
            return f"Unsupported type: {key_type}"
    #except redis.RedisError as e:
    except     Exception as e:
        return f"读取失败: {str(e)}"

def userGet_redis_data(r,key):
    try:
        value = get_value(r, key)
        return value
    except Exception as e:
        errorStr = str(e)
        __writeToLog('userGet_redis_data error ' + errorStr,ConFileName)
        return None

#向指定频道发布消息：
def __publish_message(iclient,channel: str, message: str) -> None:
    try:
        #channel='device:cmd:S7200'
        data = {
            "script": "S7200.py",
            "method": "set",
            "Command": "run_device",#"set_voltage",
            "args": {"hehe": 100},
            "request_id": "uuid-1234",
            "timeout":1,
            "delay": 100
        }
        #message = json.dumps(data)


        #formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")

        #print(f"xx已发布消息到频道 {formatted_time}: [{channel}]: {message}")
        ii=iclient.publish(channel, message)  # ✅ 发布消息到指定频道

        formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        #print(f"已发布消息到频道 {formatted_time}:[{channel}]: {message}")
        return True

        # 示例：向频道 "chat:room1" 发送消息
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__publish_message error' + errorStr)
        return False


def __subscribe_channel(redis_client,channel):
    try:
        pubsub = redis_client.pubsub()
        pubsub.subscribe(*channel)
        return pubsub
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__subscribe_channel error' + errorStr)
        return None

def __Unsubscribe_channel(redis_client,channel):
    try:
        redis_client.unsubscribe(*channel)  # ✅ 取消订阅单个频道
        redis_client.close()  # ✅ 关闭 pubsub 监听
    except Exception as e:
        errorStr = str(e)
        __writeToLog('Unsubscribe_channel error' + errorStr)
        return None

def __subscribe_channel_async2(redis_client,channel,channelx,varmsg,result):
    def _listen():
        try:
            #channel = 'device:cmd:S7200'
            #pubsub = redis_client.pubsub()
            #pubsub.subscribe(*channel)
            start_time = datetime.now()
            end_time = datetime.now()
            time_diff=0
            iHas=0
            print('start_time',start_time)
            while time_diff<4:
                # 计算时间差
                end_time = datetime.now()
                time_diff = end_time - start_time
                time_diff=time_diff.total_seconds()
                msg = pubsub.get_message(
                    ignore_subscribe_messages=True,
                    timeout=0.3  # 🕒 每次最多等待1秒
                    )
                if msg:
                    #print('msg',msg)
                    if msg["type"] == "message":
                        #print(f"异步收到消息: {msg['data']}")
                        #print(f"异步收到channel: {msg['channel']}",datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
                        # 终止订阅并关闭 pubsub 连接
                        # or break
                        #pubsub.unsubscribe(channel)  # ✅ 取消订阅单个频道
                        #pubsub.close()  # ✅ 关闭 pubsub 监听
                        iHas=1
                        print('msg', msg)
                        #print('re',msg['data'])
                        #print('ree', channelx,msg['channel'])
                        json_string = json.loads(msg['data'])
                        if msg['channel'] in channelx:
                            channelx.remove(msg['channel'])
                        if 'device:ack' in msg['channel']:
                            varmsg['ackStatus'] = json_string['Status']
                        if 'device:response' in msg['channel']:
                            varmsg['responseStatus'] = json_string['status']
                        if len(channelx)<=0:
                            break
            #print(iHas,'enddddddddddddddddddddddddddddddddddddddddddddddddddd_', )
            #print('1_',channel)
            #print('2_', channelx)
            end_time = datetime.now()
            print('end_time',end_time)
            if len(channelx)>0:
                channelx.clear()
                result['key']='fail'
            else:
                result['key']='pass'
            if 111>222:  # 20250516 修改为全局检测
                pubsub.unsubscribe(*channel)  # ✅ 取消订阅单个频道
                pubsub.close()  # ✅ 关闭 pubsub 监听
        except Exception as e:
            errorStr = str(e)
            __writeToLog('subscribe_channel_async2 error' + errorStr)
            traceback_message = traceback.format_exc()
            __writeToLog(traceback_message)

    if 111 > 222:  # 20250516 修改为全局检测
        pubsub = redis_client.pubsub()
        pubsub.subscribe(*channel)
    else:
        pubsub=redis_client

    #print('开始监听.........................'+datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
    # 启动独立线程监听
    t=threading.Thread(target=_listen, daemon=True)
    t.start()
    #t.join()

def __pubAndSubToRedis(session,sessioncmd,jsonstr,vardevName):
    try:
        # do something to redis
        idevName = vardevName  # 'S7200'

        # print('josnSSSSSSS_', datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
        channel = ["device:ack:" + idevName, "device:response:" + idevName]
        channelx = ["device:ack:" + idevName, "device:response:" + idevName]
        #channel = ["device:ack:" + idevName]
        #channelx = ["device:ack:" + idevName]
        chmsg = {'ackStatus': '', 'responseStatus': ''}
        result={}
        __subscribe_channel_async2(sessioncmd, channel, channelx, chmsg,result)  # 异步检测
        # 发布前打印
        channelpublish = "device:cmd:" + idevName
        for i in range(1):
            # r = __get_redis_connection()
            # __subscribe_channel_async(r, "device:cmd:S7200")
            # sleep(1)
            formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
            # print('start_', formatted_time)
            # print_redis_data(r2)
            __publish_message(session, channelpublish, jsonstr)
            # 发布后打印
            formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
            print('start22_', formatted_time)
            # __writeToLog('start22_')
        istart_time = datetime.now()
        itime_diff = 0
        iHas = 0
        while (len(channelx) > 0) and (itime_diff <= 10):
            sleep(0.005)
            iend_time = datetime.now()
            itime_diff = iend_time - istart_time
            itime_diff = itime_diff.total_seconds()
        # print('josnendddddddd_', datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
        if ('key'in result)and ( result['key']=='pass'):
            return (conFuncErr, '', False)
        else:
            return (conFuncErr, '', True)

    except Exception as e:
        errorStr = str(e)
        __writeToLog(idevName+'__pubAndSubToRedis error ' + errorStr,ConFileName)
        return (conFuncErr, '', False)



# 示例：异步监听

