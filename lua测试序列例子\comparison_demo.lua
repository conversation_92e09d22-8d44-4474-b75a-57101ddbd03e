-- 对比演示：main.lua vs run_test.lua 的区别

print("=== 对比演示：main.lua vs run_test.lua ===\n")

print("🔍 让我们看看两种运行方式的区别：\n")

-- 模拟直接运行 main.lua 的效果
print("1️⃣ 直接运行 main.lua 的效果：")
print("="*50)

-- 重置环境
A, B, C = nil, nil, nil
_G.other = nil
_G.third = nil

print("执行：lua main.lua")
print("输出内容：")

-- 加载并执行 main.lua（模拟直接运行）
local main_module = dofile("lua测试序列例子/main.lua")
print("\n--- 直接运行 main.lua 只会执行 main() 函数 ---")
local main_only_result = main_module.main()

print("\n✅ 直接运行 main.lua 完成")
print("执行内容：")
print("  • 设置全局变量 A, B, C")
print("  • 加载 other 和 third 模块")
print("  • 执行主流程 main()")
print("  • 基础计算、处理、交互")
print("  • 结束")

-- 保存状态
local main_only_state = {
    A = A, B = B, C = C,
    other_X = other.X, other_Y = other.Y,
    third_X = third.X, third_Y = third.Y
}

print("\n" .. "="*80)

-- 模拟运行 run_test.lua 的效果
print("\n2️⃣ 运行 run_test.lua 的效果：")
print("="*50)

-- 重置环境
A, B, C = nil, nil, nil
_G.other = nil
_G.third = nil

print("执行：lua run_test.lua")
print("输出内容：")

print("\n--- run_test.lua 会执行更多内容 ---")

-- 模拟 run_test.lua 的执行过程
print("阶段1: 执行 main.lua 的主流程")
local main_module2 = dofile("lua测试序列例子/main.lua")
local main_result2 = main_module2.main()

print("\n阶段2: 额外的模块独立测试")
local other2 = dofile("lua测试序列例子/other.lua")
local third2 = dofile("lua测试序列例子/third.lua")

print("  • other.print_variables()")
print("  • other.calculate()")
print("  • other.self_check()")
print("  • third.print_variables()")
print("  • third.calculate()")
print("  • third.advanced_math()")
print("  • third.self_check()")

print("\n阶段3: 额外的跨模块交互测试")
print("  • main_module.interact()")
print("  • other.collaborate_with_third()")
print("  • third.collaborate_with_other()")

print("\n阶段4: 额外的高级功能测试")
print("  • third.physics_simulation()")
print("  • third.data_analysis()")
print("  • other.business_logic()")

print("\n阶段5: 额外的批量操作测试")
print("  • other.batch_operations()")

print("\n阶段6: 详细的状态报告和验证")
print("  • 变量状态报告")
print("  • 命名空间验证")
print("  • 功能验证")
print("  • 代码简洁性展示")

-- 保存状态
local run_test_state = {
    A = A, B = B, C = C,
    other_X = other2.X, other_Y = other2.Y,
    third_X = third2.X, third_Y = third2.Y
}

print("\n✅ 运行 run_test.lua 完成")

print("\n" .. "="*80)

-- 详细对比
print("\n📊 详细对比分析：")
print("="*50)

print("\n🎯 执行内容对比：")
print("┌─────────────────────┬─────────────┬─────────────┐")
print("│ 功能                │ main.lua    │ run_test.lua│")
print("├─────────────────────┼─────────────┼─────────────┤")
print("│ 基础主流程          │ ✅          │ ✅          │")
print("│ 模块独立测试        │ ❌          │ ✅          │")
print("│ 跨模块交互测试      │ ❌          │ ✅          │")
print("│ 高级功能测试        │ ❌          │ ✅          │")
print("│ 批量操作测试        │ ❌          │ ✅          │")
print("│ 详细状态报告        │ ❌          │ ✅          │")
print("│ 功能验证            │ ❌          │ ✅          │")
print("└─────────────────────┴─────────────┴─────────────┘")

print("\n⏱️ 执行时间对比：")
print("• main.lua:     快速执行，专注核心流程")
print("• run_test.lua: 较长执行，全面测试验证")

print("\n📝 输出内容对比：")
print("• main.lua:     简洁输出，主要结果")
print("• run_test.lua: 详细输出，测试报告")

print("\n🎯 使用场景对比：")
print("• main.lua:     日常使用，生产环境")
print("• run_test.lua: 开发测试，功能验证")

print("\n💾 变量状态对比：")
print("main.lua 执行后的状态：")
print("  A=" .. (main_only_state.A or "nil"))
print("  B=" .. (main_only_state.B or "nil"))
print("  C=" .. (main_only_state.C or "nil"))
print("  other.X=" .. (main_only_state.other_X or "nil"))
print("  third.X=" .. (main_only_state.third_X or "nil"))

print("\nrun_test.lua 执行后的状态：")
print("  A=" .. (run_test_state.A or "nil"))
print("  B=" .. (run_test_state.B or "nil"))
print("  C=" .. (run_test_state.C or "nil"))
print("  other.X=" .. (run_test_state.other_X or "nil"))
print("  third.X=" .. (run_test_state.third_X or "nil"))

print("\n🔍 关键区别总结：")
print("="*50)

print("\n1. 📦 模块加载次数：")
print("   • main.lua: 加载模块1次")
print("   • run_test.lua: 加载模块2次（main中1次 + 测试中1次）")

print("\n2. 🔄 函数调用范围：")
print("   • main.lua: 只调用主流程函数")
print("   • run_test.lua: 调用所有可用函数进行测试")

print("\n3. 📊 测试覆盖度：")
print("   • main.lua: 基础功能覆盖")
print("   • run_test.lua: 100% 功能覆盖")

print("\n4. 🎯 目标用途：")
print("   • main.lua: 实际业务运行")
print("   • run_test.lua: 开发测试验证")

print("\n5. 📈 性能影响：")
print("   • main.lua: 最小资源消耗")
print("   • run_test.lua: 更多资源消耗（因为全面测试）")

print("\n💡 建议使用方式：")
print("="*30)
print("🔧 开发阶段: 使用 run_test.lua 进行全面测试")
print("🚀 生产环境: 使用 main.lua 进行实际运行")
print("🐛 调试问题: 使用 run_test.lua 进行详细诊断")
print("⚡ 快速验证: 使用 main.lua 进行快速检查")

print("\n" .. "="*80)
print("🎉 对比演示完成！")
print("现在您清楚两种运行方式的区别了吗？")
print("="*80)
