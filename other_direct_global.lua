-- other模块 - 直接全局变量版本
-- 删除了 local other = {}，直接使用全局变量

print("=== other_direct_global.lua 开始加载 ===")

-- 方式1：直接全局变量 A = 100
A = 100
B = 200
C = 300
name = "other_direct_global"
version = "3.0"

print("设置了全局变量 A, B, C")

-- 全局函数
function print_variables()
    print("=== other模块变量（全局版本）===")
    print("A = " .. A)
    print("B = " .. B)
    print("C = " .. C)
    print("name = " .. name)
end

function calculate()
    local sum = A + B + C
    local product = A * B * C
    
    print("other模块计算结果:")
    print("sum = " .. sum)
    print("product = " .. product)
    
    return {sum = sum, product = product}
end

-- 访问其他模块的函数
function use_third_variables()
    print("=== other模块访问third模块 ===")
    
    if _G.third then
        print("访问 third.X = " .. third.X)
        print("访问 third.Y = " .. third.Y)
        
        -- 修改third模块的变量
        third.X = third.X + 50
        print("修改third.X为: " .. third.X)
    else
        print("third模块未找到")
    end
end

-- 与third模块协作
function collaborate_with_third()
    print("=== other与third协作（全局变量版本）===")
    
    if not _G.third then
        print("third模块未找到")
        return 0
    end
    
    -- 数据交换
    local temp = A
    A = third.X
    third.X = temp
    
    print("交换变量: A <-> third.X")
    print("现在 A = " .. A)
    print("现在 third.X = " .. third.X)
    
    return A + B + C
end

print("other_direct_global.lua 加载完成")

-- 注意：这种方式不返回表，因为所有东西都是全局的
