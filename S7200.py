#import ctypes
import time
#from pickle import G<PERSON><PERSON>BA<PERSON>
from time import sleep
#import tkinter as tk

#from Demos.win32ts_logoff_disconnected import session
from pymodbus.client import ModbusTcpClient
#from tkinter import messagebox
import os
import socket
import json
from datetime import datetime
import AteR<PERSON><PERSON> as CommonUser
from pymodbus.pdu.mei_message import ReadDeviceInformationRequest

conWriteerr=11  #写异常
conReadErr=22   #读异常
conFuncErr=33  #抛出异常

#version_info = 3.9.13.final.0

ConJosnOrCommand=1   # 0_直接下发设备; 1_转换为Josn(发给第三方redis调用)
isDebug=0  # 1_DEBUG
rrConn=None
###############################版本号记录########################################################
#20250630_ 参数控制时，一些模式下分支变量未定义赋值bug优化

########################################################################################
# 创建一个隐藏的主窗口
#root = tk.Tk()
#root.withdraw()

#写日志文件
def __writeToLog(tempstr):
    try:
        slog_path = os.getcwd()
        slog_path = slog_path + '\pyLog'
        slog_path = r'D:\pyLog'
        if not os.path.exists(slog_path):
            os.makedirs(slog_path, exist_ok=True)

        now = datetime.now()
        # 格式化时间为字符串
        formatted_time = now.strftime('%Y-%m-%d') + '_S7200.log'

        slog_path = slog_path + '\\' + formatted_time

        # 打开文件，模式为 'w' 表示写模式，如果文件已存在则会被覆盖
        with open(slog_path, 'a', encoding='utf-8') as file:
            # 写入文本到文件
            now = datetime.now()
            # 格式化时间为字符串
            sformatted_time = now.strftime('%Y-%m-%d %H:%M:%S.%f  ')
            file.write(sformatted_time + tempstr + '\n')
    except Exception as e:
        errorStr = str(e)
        return (conFuncErr, errorStr, True)

def __Device_ErrorWrite(seq_context,WriteMode,varvalue):
    try:
        ivarvalue=int(varvalue)
        if seq_context is not None:
            if WriteMode==0:
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.DeviceState", 1, ivarvalue)
            elif WriteMode==1:
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.DeviceState", 1, ivarvalue)
                #seq_context.SetValNumber("StationGlobals.Devices.高压直流源载.WriteState", 1, ivarvalue)
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__Device_ErrorWrite error ' + errorStr)
        return (conFuncErr, errorStr, True)


#打开设备
def __open_instrument_resource(seq_context,defaultRM,resource_name):#端口号和IP地址
    try:
        # close_instrument_resource(session)#上一次设备没有关机，这次来停止
        session = ModbusTcpClient(defaultRM, port=int(resource_name))
        status = session.connect()
        session.retries=1  #重试次数
        session.timeout=0.2  #超时

        if not status:
            for i in range(1):  # 最多重试3次
                status = session.connect()
                if status:
                    continue
            if not status:
                # messagebox.showinfo("提示", f"无法打开仪器资源{resource_name}")
                __Device_ErrorWrite(seq_context, 1, 3)
                return None
            else:
                __Device_ErrorWrite(seq_context, 1, 1)
                return session
        else:
            __Device_ErrorWrite(seq_context, 1, 1)
            __Remote_link_device(seq_context, session)
            return session
    except Exception as e:
        errorStr = str(e)
        __writeToLog('open_instrument_resource error ' + errorStr)
        __Device_ErrorWrite(seq_context, 0, 2)
        return None
#打开设备
def ts_open_instrument_resource(seq_context,defaultRM,resource_name):#端口号和IP地址
    try:
        global ConJosnOrCommand
        if seq_context is not None:
            ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)

        ConJosnOrCommand=int(ConJosnOrCommand)
        if ConJosnOrCommand == 0:
            r=__open_instrument_resource(seq_context,defaultRM,resource_name)
            return r
        else:  #建立外部redis连接
            r = CommonUser.__get_redis_connection()
            #  远程连接
            return r
    except Exception as e:
        errorStr = str(e)
        __writeToLog('open_instrument_resource error ' + errorStr)
        __Device_ErrorWrite(seq_context, 0, 2)
        return None

def ts_NewConn_Query(seq_context):#端口号和IP地址
    try:
        r = CommonUser.__get_redis_connection()
        return r
    except Exception as e:
        errorStr = str(e)
        __writeToLog('ts_NewConn_Query error ' + errorStr)
        __Device_ErrorWrite(seq_context, 0, 2)
        return None

def ts_NewConn_Monitor(seq_context):#端口号和IP地址
    try:
        # do something to redis
        if seq_context is not None:
            idevName=seq_context.GetValString("StationGlobals.Devices.S7200.DeviceName", '1')
        else:
            idevName ='S7200_3'

        # 安装依赖：pip install redis prettytable
        #r = CommonUser.__get_redis_connection()
        channel = ["device:ack:"+idevName, "device:response:"+idevName]
        r = CommonUser.__get_redis_connection()
        rr=CommonUser.__subscribe_channel(r, channel)  # 异步检测
        return rr
    except Exception as e:
        errorStr = str(e)
        __writeToLog('ts_NewConn_Monitor error ' + errorStr)
        __Device_ErrorWrite(seq_context, 0, 2)
        return None

#关闭设备
def ts_close_instrument_resource(seq_context,session):
    try:
        global ConJosnOrCommand
        if seq_context is not None:
            ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)
        ConJosnOrCommand = int(ConJosnOrCommand)
        if ConJosnOrCommand==0:
            if session:
                #result = session.write_registers(address=2000, values=[0])
                session.close()
            return 1
        else:
            CommonUser.__redis_disconnection(session)
            return 0
    except Exception as e:
        errorStr = str(e)
        __writeToLog('close_instrument_resource error ' + errorStr)

#关闭redis设备
def ts_close_redis(seq_context,sessioncmd,sessionQ):
    try:
        global ConJosnOrCommand
        if seq_context is not None:
            ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)
        ConJosnOrCommand = int(ConJosnOrCommand)
        CommonUser.__redis_disconnection(sessioncmd)
        CommonUser.__redis_disconnection(sessionQ)
        return 0
    except Exception as e:
        errorStr = str(e)
        __writeToLog('ts_close_redis error ' + errorStr)




#远程设备控制
def __Remote_link_device(seq_context,session):
    try:
         if session:
            result = session.write_registers(address=2000, values=[1])
            if  result.isError():
                #messagebox.showinfo("提示", f"写入寄存器失败{2000}: {result}")
                __Device_ErrorWrite(seq_context, 1, 3)
                errret = (conWriteerr, '控制异常', True)
            else:
                __Device_ErrorWrite(seq_context, 1, 1)
                errret = (conWriteerr, '', False)
         else:
            __Device_ErrorWrite(seq_context, 1, 2)
            errret = (conWriteerr, '无连接4', True)
         return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__Remote_link_device error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)

#运行设备控制
def __run_device(seq_context,session):
    try:
         if session:
            result = session.write_registers(address=2001, values=[1])#On
            if  result.isError():
                #messagebox.showinfo("提示", f"写入寄存器失败{2001}: {result}")
                __Device_ErrorWrite(seq_context,1,3)
                errret=(conWriteerr, '控制异常', True)
            else:
                __Device_ErrorWrite(seq_context, 1, 1)
                errret = (conWriteerr, '', False)
         else:
            __Device_ErrorWrite(seq_context, 1, 2)
            errret= (conWriteerr, '无连接55', True)
         return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('run_device error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)


#停止设备控制
def __stop_device(seq_context,session):
    try:
        if session:
            result = session.write_registers(address=2001, values=[0])#off
            if  result.isError():
                #messagebox.showinfo("提示", f"写入寄存器失败{2001}: {result}")
                __Device_ErrorWrite(seq_context, 1, 3)
                errret = (conWriteerr, '控制异常', True)
            else:
                __Device_ErrorWrite(seq_context, 1, 1)
                errret = (conWriteerr, '', False)
        else:
            __Device_ErrorWrite(seq_context, 1, 2)
            errret = (conWriteerr, '无连接6', True)
        return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('stop_device error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)
#复位设备
def __reset_device(seq_context,session):
    try:
        if session:
            result = session.write_registers(address=2002, values=[1])
            if  result.isError():
                #messagebox.showinfo("提示", f"写入寄存器失败{2002}: {result}")
                __Device_ErrorWrite(seq_context, 1, 3)
                errret = (conWriteerr, '控制异常', True)
            else:
                __Device_ErrorWrite(seq_context, 1, 1)
                errret = (conWriteerr, '', False)
        else:
            __Device_ErrorWrite(seq_context, 1, 2)
            errret = (conWriteerr, '无连接7', True)
        return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('reset_device error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)

def ts_Remote_On(seq_context,session,sessioncmd):
    global ConJosnOrCommand
    if seq_context is not None:
        ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)
    ConJosnOrCommand = int(ConJosnOrCommand)
    if ConJosnOrCommand==0:
        errret=__Remote_link_device(seq_context,session)
    else:
        DataSet={}
        DataSet['script']='S7200.py'
        DataSet['method'] = 'set'
        DataSet['Command'] = 'Remote_device'
        DataSet['args'] = {}
        DataSet['request_id'] = "uuid-1234"
        DataSet['timeout'] = 1
        DataSet['delay'] = 100
        json_string = json.dumps(DataSet)
        errret=__ts_josn_set(seq_context,session,sessioncmd,json_string)
    return errret

#  session  当ConJosnOrCommand
def ts_run_device(seq_context,session,sessioncmd):
    global ConJosnOrCommand
    if seq_context is not None:
        ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)
    ConJosnOrCommand = int(ConJosnOrCommand)
    if ConJosnOrCommand==0:
        errret=__run_device(seq_context,session)
    else:
        DataSet={}
        DataSet['script']='S7200.py'  #'E5000LocalVisa2.py'#
        DataSet['method'] = 'set'
        DataSet['Command'] = 'run_device'
        DataSet['args'] = {}
        DataSet['request_id'] = "uuid-1234"
        DataSet['timeout'] = 1
        DataSet['delay'] = 100

        json_string = json.dumps([DataSet,DataSet])
        print('json_string',json_string)
        errret=__ts_josn_set(seq_context,session,sessioncmd,json_string)
    return errret

def ts_reset_device(seq_context,session,sessioncmd):
    global ConJosnOrCommand
    if seq_context is not None:
        ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)
    ConJosnOrCommand = int(ConJosnOrCommand)
    if ConJosnOrCommand==0:
        errret= __reset_device(seq_context,session)
    else:
        DataSet={}
        DataSet['script']='S7200.py'
        DataSet['method'] = 'set'
        DataSet['Command'] = 'reset_device'
        DataSet['args'] = {}
        DataSet['request_id'] = "uuid-1234"
        DataSet['timeout'] = 1
        DataSet['delay'] = 100
        json_string = json.dumps(DataSet)
        errret=__ts_josn_set(seq_context,session,sessioncmd,json_string)
    return errret

def ts_stop_device(seq_context,session,sessioncmd):
    global ConJosnOrCommand
    if seq_context is not None:
        ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)
    ConJosnOrCommand = int(ConJosnOrCommand)
    if ConJosnOrCommand==0:
        errret=__stop_device(seq_context,session)
    else:
        DataSet={}
        DataSet['script']='S7200.py'
        DataSet['method'] = 'set'
        DataSet['Command'] = 'stop_device'
        DataSet['args'] = {}
        DataSet['request_id'] = "uuid-1234"
        DataSet['timeout'] = 1
        DataSet['delay'] = 100
        json_string = json.dumps(DataSet)
        errret=__ts_josn_set(seq_context,session,sessioncmd,json_string)
    return errret

 #步阶参数设置-模式，电源，电压频率，相位
def __ParasSet_By_step(seq_context,StepMode,Value,session):
    try:
        if session:
            #电源相关设置
            if StepMode == "SCC":
                high, low=__split_i32_value(int(Value*10000))
                values_to_write = [high, low]
                result = session.write_registers(address=2330, values=values_to_write)#电源CC电流设定
            elif StepMode == "SCV":
                high, low=__split_i32_value(int(Value*10000))
                values_to_write = [high, low]
                result = session.write_registers(address=2300, values=values_to_write)#电源CV电压设定
            elif StepMode == "LCC":
                high, low=__split_i32_value(int(Value*10000))
                values_to_write = [high, low]
                result = session.write_registers(address=4010, values=values_to_write) #负载恒流设置
            elif StepMode == "LCV":
                high, low=__split_i32_value(int(Value*10000))
                values_to_write = [high, low]
                result = session.write_registers(address=4010, values=values_to_write) #负载恒流设置
            elif StepMode == "LCR":
                high, low=__split_i32_value(int(Value*10000))
                values_to_write = [high, low]
                result = session.write_registers(address=4010, values=values_to_write) #负载恒流设置
            if result.isError():
                errret = (conWriteerr, '控制异常', True)
                __Device_ErrorWrite(seq_context, 1, 3)
            else:
                errret = (conWriteerr, '', False)
                __Device_ErrorWrite(seq_context, 1, 1)
            return errret
        else:
            errret =(conWriteerr, '无连接8', True)
            return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('ParasSet_By_step error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)

def ts_ParasSet_By_step(seq_context,StepMode,Value,session,sessioncmd):
    global ConJosnOrCommand
    if seq_context is not None:
        ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)
    ConJosnOrCommand = int(ConJosnOrCommand)
    if ConJosnOrCommand==0:
        errret=__ParasSet_By_step(seq_context,StepMode,Value,session)
    else:
        DataSet={}
        DataSet['script']='S7200.py'
        DataSet['method'] = 'set'
        DataSet['Command'] = 'ParasSet_By_step'
        DataSet['args'] = {}
        DataSet['args']['StepMode']=StepMode
        DataSet['args']['Value']=Value
        DataSet['request_id'] = "uuid-1234"
        DataSet['timeout'] = 1
        DataSet['delay'] = 100
        json_string = json.dumps(DataSet)
        errret=__ts_josn_set(seq_context,session,sessioncmd,json_string)
    return errret


#参数设置-模式，电源，电压频率，相位
def __ParasSet_ProtectData(seq_context,session,dataArgs):
    try:
        if seq_context is not None:
            OVPValue = seq_context.GetValNumber("Step.设置.过压点",0)
            OCPValue = seq_context.GetValNumber("Step.设置.过流点",0)
            OPPValue = seq_context.GetValNumber("Step.设置.过功率点",0)
        else:
            OVPValue = dataArgs['args']['OVP']
            OCPValue =dataArgs['args']['OCP']
            OPPValue = dataArgs['args']['OPP']


        OVPValue = int(OVPValue*10000)
        OCPValue =int(OCPValue*10000)
        OPPValue = int(OPPValue*10000)

        high, low = __split_i32_value(OVPValue)
        values_to_write = [high, low]
        result = session.write_registers(address=2052, values=values_to_write)  # 电源OVP电流设定

        high, low = __split_i32_value(OCPValue)
        values_to_write = [high, low]
        result = session.write_registers(address=2057, values=values_to_write)  # 电源OCP电流设定

        high, low = __split_i32_value(OPPValue)
        values_to_write = [high, low]
        result = session.write_registers(address=2062, values=values_to_write)  # 电源OPP电流设定

        if result.isError():
            __Device_ErrorWrite(seq_context, 1, 3)
            errret = (conWriteerr, '控制异常', True)
        else:
            __Device_ErrorWrite(seq_context, 1, 1)
            errret = (conWriteerr, '', False)
        return errret

    except Exception as e:
        errorStr = str(e)
        __writeToLog('ParasSet_ProtectData error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)

#参数设置-模式，电源，电压频率，相位
def __ParasSet(seq_context,session,dataArgs):
    try:
        if seq_context is not None:
            mode_num = seq_context.GetValNumber("Step.设置.设备模式",0)
            soure_mode_str = seq_context.GetValString("Step.设置.电源模式","0")
            load_mode_str = seq_context.GetValString("Step.设置.负载模式","0")

            #电源
            DC_CC_Cur_Set = seq_context.GetValNumber("Step.设置.电源电流", 0)#CC电流值设定
            U_Max_Set = seq_context.GetValNumber("Step.设置.电压上限", 0)
            U_Min_Set = seq_context.GetValNumber("Step.设置.电压下限", 0)

            DC_CV_Vol_Set = seq_context.GetValNumber("Step.设置.电源电压", 0)#CV电压值设定
            I_Max_Set = seq_context.GetValNumber("Step.设置.电流上限", 0)
            I_Min_Set = seq_context.GetValNumber("Step.设置.电流下限", 0)

            P_Max_Set = seq_context.GetValNumber("Step.设置.功率上限", 0)
            P_Min_Set = seq_context.GetValNumber("Step.设置.功率下限", 0)

            #负载
            short = seq_context.GetValNumber("Step.设置.负载短路", 0)#短路设置

            Load_CC_Cur_Set = seq_context.GetValNumber("Step.设置.负载电流", 0)#CC电流值设定

            Load_CV_Vol_Set = seq_context.GetValNumber("Step.设置.负载电压", 0)#CV电压值设定

            Load_CR_Res_Set = seq_context.GetValNumber("Step.设置.负载电阻", 0)#CR电阻值设定

        else:
            '''
            mode_num=dataArgs['args']['mode']
            soure_mode_str=dataArgs['args']['soure_mode']
            load_mode_str=dataArgs['args']['load_mode']
            DC_CC_Cur_Set=dataArgs['args']['DC_Cur']
            U_Max_Set=dataArgs['args']['U_Max']
            U_Min_Set=dataArgs['args']['U_Min']
            DC_CV_Vol_Set=dataArgs['args']['DC_Vol']

            I_Min_Set=dataArgs['args']['I_Min']
            I_Max_Set=dataArgs['args']['I_Max']
            P_Max_Set=dataArgs['args']['P_Max']
            P_Min_Set=dataArgs['args']['P_Min']
            short=dataArgs['args']['short']
            Load_CC_Cur_Set=dataArgs['args']['Load_Cur']
            Load_CV_Vol_Set=dataArgs['args']['Load_Vol']
            Load_CR_Res_Set=dataArgs['args']['Load_R']
            '''
            mode_num=1
            soure_mode_str='CC'
            load_mode_str='CC'
            DC_CC_Cur_Set=0
            U_Max_Set=0
            U_Min_Set=0
            DC_CV_Vol_Set=0

            I_Min_Set=0
            I_Max_Set=0
            P_Max_Set=0
            P_Min_Set=0
            short=0
            Load_CC_Cur_Set=0
            Load_CV_Vol_Set=0
            Load_CR_Res_Set=0

        mode_num=int(mode_num)
        short = int(short)  # 短路设置

        if mode_num==0:#电源模式
           if soure_mode_str=="CC":
              work_mode=1
           elif soure_mode_str=="CV":
              work_mode=0
           else:
               work_mode = 255
        elif mode_num==1:#负载模式
            if load_mode_str=="CC":
              work_mode=20
            elif load_mode_str=="CV":
              work_mode=21
            elif load_mode_str=="CR":
              work_mode=23
            else:
                work_mode = 255
        else:
            work_mode = 255
        # else:
        #    messagebox.showinfo("提示", "模式设置错误")



        #测试专用，发布时屏蔽----------
        # work_mode = 5
        # #电源
        # DC_CC_Cur_Set = 5
        # DC_CV_Vol_Set = 5
        # #负载
        # short = 1
        # Load_CC_Cur_Set = 5
        # Load_CV_Vol_Set = 5
        # Load_CR_Res_Set = 5

        if session:
            try:
                #电源相关设置
                if work_mode<255:
                    result = session.write_registers(address=2050, values=[int(work_mode)])#工作模式
                if mode_num==0 and soure_mode_str=="CC":
                    high, low=__split_i32_value(int(DC_CC_Cur_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=2330, values=values_to_write)#电源CC电流设定
                    high, low=__split_i32_value(int(U_Max_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=2332, values=values_to_write)#电源CC电压上限
                    high, low=__split_i32_value(int(U_Min_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=2334, values=values_to_write)#电源CC电压下限
                    high, low=__split_i32_value(int(P_Max_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=2336, values=values_to_write)#电源CC功率上限
                    high, low=__split_i32_value(int(P_Min_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=2338, values=values_to_write)#电源CC功率下限
                if mode_num==0 and soure_mode_str=="CV":
                    high, low=__split_i32_value(int(DC_CV_Vol_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=2300, values=values_to_write)#电源CV电压设定
                    high, low=__split_i32_value(int(I_Max_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=2302, values=values_to_write)#电源CV电流上限
                    high, low=__split_i32_value(int(I_Min_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=2304, values=values_to_write)#电源CV电流下限
                    high, low=__split_i32_value(int(P_Max_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=2306, values=values_to_write)#电源CV功率上限
                    high, low=__split_i32_value(int(P_Min_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=2308, values=values_to_write)#电源CV功率下限

                #负载相关设置
                if mode_num==1 or load_mode_str=="Short":
                    result = session.write_registers(address=2007, values=[int(short)]) #短路设置-0短路-1开路
                if mode_num==1 or load_mode_str=="CC":
                    high, low=__split_i32_value(int(Load_CC_Cur_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=4010, values=values_to_write) #负载恒流设置
                if mode_num==1 or load_mode_str=="CV":
                    high, low=__split_i32_value(int(Load_CV_Vol_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=4012, values=values_to_write) #负载恒压设置
                if mode_num==1 or load_mode_str=="CR":
                    high, low=__split_i32_value(int(Load_CR_Res_Set*10000))
                    values_to_write = [high, low]
                    result = session.write_registers(address=4016, values=values_to_write) #负载恒阻设置
                if result.isError():
                    __Device_ErrorWrite(seq_context, 1, 3)
                    errret = (conWriteerr, '控制异常', True)
                else:
                    __Device_ErrorWrite(seq_context, 1, 1)
                    errret = (conWriteerr, '', False)
                return errret
            except Exception as e:
              #messagebox.showinfo("提示", f"写入寄存器失败{load_mode_str}--{e}")
              __Device_ErrorWrite(seq_context,1,2)
              errorStr = str(e)
              __writeToLog('ParasSet error ' + errorStr)
              return (conFuncErr,errorStr,True)
        else:
            __Device_ErrorWrite(seq_context,1,2)
            errret=(conWriteerr,'无连接9',True)
            return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('ParasSet error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)

def ts_ParasSet_ProtectData(seq_context,session,sessioncmd):
    global ConJosnOrCommand
    if seq_context is not None:
        ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)

    ConJosnOrCommand = int(ConJosnOrCommand)
    if seq_context is not None:
        ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)
    if ConJosnOrCommand == 0:
        errret=__ParasSet_ProtectData(seq_context,session,None)
    else:
        if seq_context is not None:
            OVPValue = seq_context.GetValNumber("Step.设置.过压点",1)
            OCPValue = seq_context.GetValNumber("Step.设置.过流点",1)
            OPPValue = seq_context.GetValNumber("Step.设置.过功率点",1)
        else:
            OVPValue = 0
            OCPValue = 0
            OPPValue = 0

        DataSet={}
        DataSet['script']='S7200.py'
        DataSet['method'] = 'set'
        DataSet['Command'] = 'ParasSet_ProtectData'
        DataSet['args'] = {}
        DataSet['args']['OVP']=OVPValue
        DataSet['args']['OCP'] = OCPValue
        DataSet['args']['OPP'] = OPPValue

        DataSet['request_id'] = "uuid-1234"
        DataSet['timeout'] = 1
        DataSet['delay'] = 100
        json_string = json.dumps(DataSet)
        errret=__ts_josn_set(seq_context,session,sessioncmd,json_string)
    return errret

def ts_ParasSet(seq_context,session,sessioncmd):
    global ConJosnOrCommand
    if seq_context is not None:
        ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)

    ConJosnOrCommand = int(ConJosnOrCommand)
    if ConJosnOrCommand == 0:
        errret=__ParasSet(seq_context,session,None)
    else:
        if seq_context is not None:
            mode_num = seq_context.GetValNumber("Step.设置.设备模式",0)
            soure_mode_str = seq_context.GetValString("Step.设置.电源模式","0")
            load_mode_str = seq_context.GetValString("Step.设置.负载模式","0")

            #电源
            DC_CC_Cur_Set = seq_context.GetValNumber("Step.设置.电源电流", 0)#CC电流值设定
            U_Max_Set = seq_context.GetValNumber("Step.设置.电压上限", 0)
            U_Min_Set = seq_context.GetValNumber("Step.设置.电压下限", 0)

            DC_CV_Vol_Set = seq_context.GetValNumber("Step.设置.电源电压", 0)#CV电压值设定
            I_Max_Set = seq_context.GetValNumber("Step.设置.电流上限", 0)
            I_Min_Set = seq_context.GetValNumber("Step.设置.电流下限", 0)

            P_Max_Set = seq_context.GetValNumber("Step.设置.功率上限", 0)
            P_Min_Set = seq_context.GetValNumber("Step.设置.功率下限", 0)
            #负载
            short = seq_context.GetValNumber("Step.设置.负载短路", 0)#短路设置

            Load_CC_Cur_Set = seq_context.GetValNumber("Step.设置.负载电流", 0)#CC电流值设定

            Load_CV_Vol_Set = seq_context.GetValNumber("Step.设置.负载电压", 0)#CV电压值设定

            Load_CR_Res_Set = seq_context.GetValNumber("Step.设置.负载电阻", 0)#CR电阻值设定
        else:
            mode_num = 0
            soure_mode_str = 'CC'
            load_mode_str = 'CC'
            #电源
            DC_CC_Cur_Set = 0
            U_Max_Set = 0
            U_Min_Set = 0
            DC_CV_Vol_Set = 0
            I_Max_Set = 0
            I_Min_Set = 0
            P_Max_Set = 0
            P_Min_Set = 0
            #负载
            short = 0
            Load_CC_Cur_Set =0
            Load_CV_Vol_Set = 0
            Load_CR_Res_Set = 0

        DataSet={}
        DataSet['script']='S7200.py'
        DataSet['method'] = 'set'
        DataSet['Command'] = 'ParasSet'
        DataSet['args'] = {}
        DataSet['args']['mode']=mode_num
        DataSet['args']['soure_mode'] = soure_mode_str
        DataSet['args']['load_mode'] = load_mode_str
        DataSet['args']['DC_Cur'] = DC_CC_Cur_Set
        DataSet['args']['U_Max'] = U_Max_Set
        DataSet['args']['U_Min'] = U_Min_Set
        DataSet['args']['DC_Vol'] = DC_CV_Vol_Set

        DataSet['args']['I_Min'] = I_Min_Set
        DataSet['args']['I_Max'] = I_Max_Set
        DataSet['args']['P_Max'] = P_Max_Set
        DataSet['args']['P_Min'] = P_Min_Set
        DataSet['args']['short'] = short
        DataSet['args']['Load_Cur'] = Load_CC_Cur_Set
        DataSet['args']['Load_Vol'] = Load_CV_Vol_Set
        DataSet['args']['Load_R'] = Load_CR_Res_Set

        DataSet['request_id'] = "uuid-1234"
        DataSet['timeout'] = 1
        DataSet['delay'] = 100
        json_string = json.dumps(DataSet)
        errret=__ts_josn_set(seq_context,session,sessioncmd,json_string)
    return errret

def UnSValueToSingnedValue(value):
    try:
        if value & 0x80000000:  # 检查最高位是否为1
            s32_value = value - 0x100000000  # 转换为负数
        else:
            s32_value = value  # 保持原值
        return s32_value
    except Exception as e:
        errorStr = str(e)
        __writeToLog('UnSValueToSingnedValue error ' + errorStr)

def __read_data_device(seq_context,session,vardataDevice):
    try:
        errvalue=0
        errret=__read_error_device(seq_context,session,vardataDevice)#读取故障信息
        read_result = session.read_holding_registers(address=1100, count=62)
        if ((errret[2]==False)and(not read_result.isError())):
            high_word=read_result.registers[0]
            low_word=read_result.registers[1]
            u32_value = (high_word << 16) + low_word
            Vol = u32_value * 0.0001
            high_word=read_result.registers[2]
            low_word=read_result.registers[3]
            u32_value = (high_word << 16) + low_word
            u32_value = UnSValueToSingnedValue(u32_value)
            Cur= u32_value *0.0001 
            high_word=read_result.registers[4]
            low_word=read_result.registers[5]
            u32_value = (high_word << 16) + low_word
            u32_value=UnSValueToSingnedValue(u32_value)
            P = u32_value* 0.1 

            result=read_result.registers[61]
            vardataDevice['State'] = result
            errvalue = 1
            if seq_context is not None:
                if result & (1 << 4)==16:#查询第4位是否为1，如果是，则表示设备在线
                   seq_context.SetValBoolean("StationGlobals.Devices.直流源载_1.状态.运行状态", True, True)
                else:
                   seq_context.SetValBoolean("StationGlobals.Devices.直流源载_1.状态.运行状态", True, False)

                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.电压", 1, Vol)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.电流", 1, Cur)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.功率", 1, P)

            #测试专用，发布时屏蔽---------------------
            #print(f"Cur: {Cur}, Vol: {Vol}, P: {P}")
            errvalue = 2
            __Device_ErrorWrite(seq_context,1,1)
            errret = (conReadErr, '', False)
            vardataDevice['Vol']=Vol
            vardataDevice['Cur']=Cur
            vardataDevice['Po']=P
            errvalue = 3
            vardataDevice['Time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            errvalue = 4

        else:
            #messagebox.showinfo("提示", f"读取错误: {read_result}")
            __Device_ErrorWrite(seq_context,1,2)
            errret = (conReadErr, '读取异常', True)
        return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('read_data_device error ' + errorStr)
        errret = (conReadErr, '无连接1_'+str(errvalue), True)
        return errret

def ts_read_data_device(seq_context,session):
    global ConJosnOrCommand
    if seq_context is not None:
        ConJosnOrCommand = seq_context.GetValNumber("StationGlobals.Devices.DeviceOrRedis", 1)

    ConJosnOrCommand = int(ConJosnOrCommand)
    if ConJosnOrCommand==0:
        dataDevice= {}
        errret=__read_data_device(seq_context, session,dataDevice)
    else:
        errret=__ts_josn_get(seq_context,session,'')
    return errret

def __read_error_device(seq_context,session,varDataDevice):
    try:
        read_result = session.read_holding_registers(address=1170, count=25)#查询25个寄存器
        varDataDevice['FaultCode0'] = read_result.registers[0]
        varDataDevice['FaultCode1'] = read_result.registers[3]
        varDataDevice['FaultCode2'] = read_result.registers[7]
        varDataDevice['FaultCode3'] = read_result.registers[8]
        varDataDevice['FaultCode4'] = read_result.registers[13]
        varDataDevice['FaultCode5'] = read_result.registers[14]
        varDataDevice['FaultCode6'] = read_result.registers[19]
        varDataDevice['FaultCode7'] = read_result.registers[21]
        varDataDevice['FaultCode8'] = read_result.registers[23]

        if not read_result.isError():
            str_error=''
            Error0=read_result.registers[0]
            bit_values = [1 if Error0 & (1 << i) else 0 for i in range(16)]
            for index, item in enumerate(bit_values):
                if item==1:
                    if index == 0:
                        str_error += "ARM_Error1_bit0 低温保护\n"
                    elif index == 1:
                        str_error += "ARM_Error1_bit1 高温保护\n"
                    elif index == 2:
                        str_error += "ARM_Error1_bit2 风机故障\n"
                    elif index == 3:
                        str_error += "ARM_Error1_bit3 辅助电源故障\n"

            Error1=read_result.registers[3]
            bit_values = [1 if Error1 & (1 << i) else 0 for i in range(16)] #列表推导式
            for index, item in enumerate(bit_values):
                if item==1:
                    if index == 0:
                        str_error += "FPGA_Error1_bit0 参数错误\n"
                    elif index == 1:
                        str_error += "FPGA_Error1_bit1 自检错误\n"
                    elif index == 2:
                        str_error += "FPGA_Error1_bit2 直流端过压\n"
                    elif index == 3:
                        str_error += "FPGA_Error1_bit3 直流端过流\n"
                    elif index == 4:
                        str_error += "FPGA_Error1_bit4 USER_OVP\n"
                    elif index == 5:
                        str_error += "FPGA_Error1_bit5 USER_OCP\n"
                    elif index == 6:
                        str_error += "FPGA_Error1_bit6 USER_OPP\n"
                    elif index == 7:
                        str_error += "FPGA_Error1_bit7 USER_UVP\n"
                    elif index == 8:
                        str_error += "FPGA_Error1_bit8 USER_UCP\n"

            Error2=read_result.registers[7]
            bit_values = [1 if Error2 & (1 << i) else 0 for i in range(16)] #列表推导式
            for index, item in enumerate(bit_values):
                if item==1:
                    if index == 0:
                        str_error += "front_DSP_Error1_bit0 参数错误\n"
                    elif index == 1:
                        str_error += "front_DSP_Error1_bit1 自检错误\n"
                    elif index == 2:
                        str_error += "front_DSP_Error1_bit2 软起故障\n"
                    elif index == 3:
                        str_error += "front_DSP_Error1_bit3 直流欠压\n"
                    elif index == 4:
                        str_error += "front_DSP_Error1_bit4 直流过压\n"
                    elif index == 5:
                        str_error += "front_DSP_Error1_bit5 直流软件过压\n"
                    elif index == 6:
                        str_error += "front_DSP_Error1_bit6 直流过流\n"
                    elif index == 7:
                        str_error += "front_DSP_Error1_bit7 相序错误\n"
                    elif index == 8:
                        str_error += "front_DSP_Error1_bit8 电网欠频\n"
                    elif index == 9:
                        str_error += "front_DSP_Error1_bit9 电网过频\n"
                    elif index == 10:
                        str_error += "front_DSP_Error1_bit10 电网欠压\n"
                    elif index == 11:
                        str_error += "front_DSP_Error1_bit11 电网过压\n"
                    elif index == 12:
                        str_error += "front_DSP_Error1_bit12 AC1过流\n"
                    elif index == 13:
                        str_error += "front_DSP_Error1_bit13 ACDC散热器1过温\n"
                    elif index == 14:
                        str_error += "front_DSP_Error1_bit14 AC2过流\n"
                    elif index == 15:
                        str_error += "front_DSP_Error1_bit15 ACDC散热器2过温\n"

            Error3=read_result.registers[8]
            bit_values = [1 if Error3 & (1 << i) else 0 for i in range(16)] #列表推导式
            for index, item in enumerate(bit_values):
                if item==1:
                    if index == 0:
                        str_error += "front_DSP_Error2_bit0 环境过温\n"
                    elif index == 1:
                        str_error += "front_DSP_Error2_bit1 AP12V欠压\n"
                    elif index == 2:
                        str_error += "front_DSP_Error2_bit2 AP12V过压\n"
                    elif index == 3:
                        str_error += "front_DSP_Error2_bit3 DCDC故障\n"
                    elif index == 4:
                        str_error += "front_DSP_Error2_bit4 ERR01\n"
                    elif index == 5:
                        str_error += "front_DSP_Error2_bit5 软件急停\n"
                    elif index == 6:
                        str_error += "front_DSP_Error2_bit6 直流软件过流\n"
                    elif index == 7:
                        str_error += "front_DSP_Error2_bit7 AC1_A软件过流\n"
                    elif index == 8:
                        str_error += "front_DSP_Error2_bit8 AC1_B软件过流\n"
                    elif index == 9:
                        str_error += "front_DSP_Error2_bit9 AC1_C软件过流\n"
                    elif index == 10:
                        str_error += "front_DSP_Error2_bit10 AC2_A软件过流\n"
                    elif index == 11:
                        str_error += "front_DSP_Error2_bit11 AC2_B软件过流\n"
                    elif index == 12:
                        str_error += "front_DSP_Error2_bit12 AC2_C软件过流\n"
                    elif index == 13:
                        str_error += "front_DSP_Error2_bit13 TEM_LOW/OPEN\n"
                    elif index == 14:
                        str_error += "front_DSP_Error2_bit14 散热器3过温 PLL_ERR\n"
                    elif index == 15:
                        str_error += "front_DSP_Error2_bit15 散热器3过温\n"

            Error4=read_result.registers[13]
            bit_values = [1 if Error4 & (1 << i) else 0 for i in range(16)] #列表推导式
            for index, item in enumerate(bit_values):
                if item==1:
                    if index == 0:
                        str_error += "middle_DSP_Error1_bit0 参数错误\n"
                    elif index == 1:
                        str_error += "middle_DSP_Error1_bit1 自检错误\n"
                    elif index == 2:
                        str_error += "middle_DSP_Error1_bit2 Dab原边欠压\n"
                    elif index == 3:
                        str_error += "middle_DSP_Error1_bit3 Dab原边过压\n"
                    elif index == 4:
                        str_error += "middle_DSP_Error1_bit4 Dab原边硬件过压\n"
                    elif index == 5:
                        str_error += "middle_DSP_Error1_bit5 DabUp副边欠压\n"
                    elif index == 6:
                        str_error += "middle_DSP_Error1_bit6 DabUp副边过压\n"
                    elif index == 7:
                        str_error += "middle_DSP_Error1_bit7 DabMid副边欠压\n"
                    elif index == 8:
                        str_error += "middle_DSP_Error1_bit8 DabMid副边过压\n"
                    elif index == 9:
                        str_error += "middle_DSP_Error1_bit9 DabDown副边欠压\n"
                    elif index == 10:
                        str_error += "middle_DSP_Error1_bit10 DabDown副边过压\n"
                    elif index == 11:
                        str_error += "middle_DSP_Error1_bit11 Dab副边硬件过压\n"
                    elif index == 12:
                        str_error += "middle_DSP_Error1_bit12 Dab过流\n"
                    elif index == 13:
                        str_error += "middle_DSP_Error1_bit13 DabUp软起故障\n"
                    elif index == 14:
                        str_error += "middle_DSP_Error1_bit14 DabMid软起故障\n"
                    elif index == 15:
                        str_error += "middle_DSP_Error1_bit15 DabDown软起故障\n"

            Error5=read_result.registers[14]
            bit_values = [1 if Error5 & (1 << i) else 0 for i in range(16)] #列表推导式
            for index, item in enumerate(bit_values):
                if item==1:
                    if index == 5:
                        str_error += "middle_DSP_Error2_bit5 Dab散热器1过温\n"
                    elif index == 6:
                        str_error += "middle_DSP_Error2_bit6 Dab散热器2过温\n"
                    elif index == 7:
                        str_error += "middle_DSP_Error2_bit7 Dab散热器3过温\n"
                    elif index == 8:
                        str_error += "middle_DSP_Error2_bit8 AP12V欠压\n"
                    elif index == 9:
                        str_error += "middle_DSP_Error2_bit9 AP12V过压\n"
                    elif index == 10:
                        str_error += "middle_DSP_Error2_bit10 ERR01\n"
                    elif index == 11:
                        str_error += "middle_DSP_Error2_bit11 软件急停\n"
                    elif index == 12:
                        str_error += "middle_DSP_Error2_bit12 DabUp软件过流\n"
                    elif index == 13:
                        str_error += "middle_DSP_Error2_bit13 DabMid软件过流\n"
                    elif index == 14:
                        str_error += "middle_DSP_Error2_bit14 DabDown软件过流\n"

            Error6=read_result.registers[19]
            bit_values = [1 if Error6 & (1 << i) else 0 for i in range(16)] #列表推导式
            for index, item in enumerate(bit_values):
                if item==1:
                    if index == 0:
                        str_error += "back_DSP_Error1_bit0 FRAM_PARA\n"
                    elif index == 1:
                        str_error += "back_DSP_Error1_bit1 SELF_CHECK\n"
                    elif index == 2:
                        str_error += "back_DSP_Error1_bit2 SPI_ERR\n"
                    elif index == 3:
                        str_error += "back_DSP_Error1_bit3 UIN_OV\n"
                    elif index == 4:
                        str_error += "back_DSP_Error1_bit4 U0_OV\n"
                    elif index == 5:
                        str_error += "back_DSP_Error1_bit5 OC1_HW\n"
                    elif index == 6:
                        str_error += "back_DSP_Error1_bit6 OC2_HW\n"
                    elif index == 7:
                        str_error += "back_DSP_Error1_bit7 OT1\n"
            Error7=read_result.registers[21]
            bit_values = [1 if Error7 & (1 << i) else 0 for i in range(16)] #列表推导式
            for index, item in enumerate(bit_values):
                if item==1:
                    if index == 0:
                        str_error += "back_DSP2_Error1_bit0 FRAM_PARA\n"
                    elif index == 1:
                        str_error += "back_DSP2_Error1_bit1 SELF_CHECK\n"
                    elif index == 2:
                        str_error += "back_DSP2_Error1_bit2 SPI_ERR\n"
                    elif index == 3:
                        str_error += "back_DSP2_Error1_bit3 UIN_OV\n"
                    elif index == 4:
                        str_error += "back_DSP2_Error1_bit4 U0_OV\n"
                    elif index == 5:
                        str_error += "back_DSP2_Error1_bit5 OC1_HW\n"
                    elif index == 6:
                        str_error += "back_DSP2_Error1_bit6 OC2_HW\n"
                    elif index == 7:
                        str_error += "back_DSP2_Error1_bit7 OT1\n"
            Error8=read_result.registers[23]
            bit_values = [1 if Error8 & (1 << i) else 0 for i in range(16)] #列表推导式
            for index, item in enumerate(bit_values):
                if item==1:
                    if index == 0:
                        str_error += "back_DSP3_Error1_bit0 FRAM_PARA\n"
                    elif index == 1:
                        str_error += "back_DSP3_Error1_bit1 SELF_CHECK\n"
                    elif index == 2:
                        str_error += "back_DSP3_Error1_bit2 SPI_ERR\n"
                    elif index == 3:
                        str_error += "back_DSP3_Error1_bit3 UIN_OV\n"
                    elif index == 4:
                        str_error += "back_DSP3_Error1_bit4 U0_OV\n"
                    elif index == 5:
                        str_error += "back_DSP3_Error1_bit5 OC1_HW\n"
                    elif index == 6:
                        str_error += "back_DSP3_Error1_bit6 OC2_HW\n"
                    elif index == 7:
                        str_error += "back_DSP3_Error1_bit7 OT1\n"

            if seq_context is not None:
                seq_context.SetValString("StationGlobals.Devices.直流源载_1.状态.故障信息", '1', str_error)
            if str_error=='':
                if seq_context is not None:
                    seq_context.SetValBoolean("StationGlobals.Devices.直流源载_1.状态.故障状态", True, False)
            else:
                if seq_context is not None:
                    seq_context.SetValBoolean("StationGlobals.Devices.直流源载_1.状态.故障状态", True, True)
            #---测试专用，发布时屏蔽---------------
            #print(f"故障: {str_error}")
            __Device_ErrorWrite(seq_context,0,1)
            errret=(conReadErr,'',False)
        else:
            #messagebox.showinfo("提示", f"读取错误: {read_result}")
            __Device_ErrorWrite(seq_context,0,3)
            errret=(conReadErr,'无连接2',True)
        return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__read_error_device error ' + errorStr)
        __Device_ErrorWrite(seq_context, 0, 2)
        errret = (conReadErr, '无连接3', True)
        return errret

def __split_i32_value(value):
    try:
        #将 32 位有符号整数拆分为高 16 位和低 16 位
        high = (value >> 16) & 0xFFFF
        low = value & 0xFFFF
        return high, low
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__read_error_device error ' + errorStr)



'''
	jsonstr={
	"script": "s7000.py",
	"methed": "set",
	"command": "set_voltage",
	"args": {'voltage': 100, 'Curr': 10},
	"request_id": "uuid_1234",
	"timeout": 1,
	"delay": 100
	}
'''

def __ts_josn_get(seq_context,session,jsonstr):
    try:
        if seq_context is not None:
            idevName=seq_context.GetValString("StationGlobals.Devices.S7200.DeviceName", '1')
        else:
            idevName ='S7200'
        #r = CommonUser.__get_redis_connection()
        channel='device:status:'+idevName
        vardataDevice=CommonUser.userGet_redis_data(session,channel)
        if (vardataDevice is not None) and (seq_context is not None):
            ##解析
            if 'State' in vardataDevice:
                value=int(vardataDevice['State'])
                if value & (1 << 4)==16:#查询第4位是否为1，如果是，则表示设备在线
                   seq_context.SetValBoolean("StationGlobals.Devices.直流源载_1.状态.运行状态", True, True)
                else:
                   seq_context.SetValBoolean("StationGlobals.Devices.直流源载_1.状态.运行状态", True, False)

            if 'Vol' in vardataDevice:
                value = vardataDevice['Vol']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.电压", 1, value)
            if 'Cur' in vardataDevice:
                value = vardataDevice['Cur']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.电流", 1, value)
            if 'Po' in vardataDevice:
                value = vardataDevice['Po']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.功率", 1, value)
            if 'Time' in vardataDevice:
                value = vardataDevice['Time']
                seq_context.SetValString("StationGlobals.Devices.直流源载_1.状态.采集时间", '1', value)
            if 'FaultCode0' in vardataDevice:
                value = vardataDevice['FaultCode0']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.FaultCode0", 1, value)
            if 'FaultCode1' in vardataDevice:
                value = vardataDevice['FaultCode1']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.FaultCode1", 1, value)
            if 'FaultCode2' in vardataDevice:
                value = vardataDevice['FaultCode2']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.FaultCode2", 1, value)
            if 'FaultCode3' in vardataDevice:
                value = vardataDevice['FaultCode3']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.FaultCode3", 1, value)
            if 'FaultCode4' in vardataDevice:
                value = vardataDevice['FaultCode4']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.FaultCode4", 1, value)
            if 'FaultCode5' in vardataDevice:
                value = vardataDevice['FaultCode5']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.FaultCode4", 1, value)
            if 'FaultCode6' in vardataDevice:
                value = vardataDevice['FaultCode6']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.FaultCode6", 1, value)
            if 'FaultCode7' in vardataDevice:
                value = vardataDevice['FaultCode7']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.FaultCode7", 1, value)
            if 'FaultCode8' in vardataDevice:
                value = vardataDevice['FaultCode8']
                int(value)
                seq_context.SetValNumber("StationGlobals.Devices.直流源载_1.状态.FaultCode8", 1, value)
            errRet=(conReadErr, '', False)
        else:
            errRet = (conReadErr, '', True)
        print('vardataDevice',vardataDevice)
        return errRet

    except Exception as e:
        errorStr = str(e)
        __writeToLog('ts_josn_get error ' + errorStr)
        return (conFuncErr, errorStr, True)
# 发布给redis并 订阅消息
#
def __ts_josn_set(seq_context,session,sessioncmd,jsonstr):
   try:
        # do something to redis
        if seq_context is not None:
            idevName=seq_context.GetValString("StationGlobals.Devices.S7200.DeviceName", '1')
        else:
            idevName ='S7200_3'#'S7200'

        # 安装依赖：pip install redis prettytable
        #r = CommonUser.__get_redis_connection()

        #print('josnSSSSSSS_', datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
        channel = ["device:ack:"+idevName, "device:response:"+idevName]
        channelx = ["device:ack:"+idevName, "device:response:"+idevName]
        chmsg={'ackStatus':'','responseStatus':''}
        CommonUser.__subscribe_channel_async2(sessioncmd, channel, channelx,chmsg)  #异步检测
        #sleep(1)
        # 发布前打印
        channelpublish = "device:cmd:"+idevName
        for i in range(1):
            # r = __get_redis_connection()
            # __subscribe_channel_async(r, "device:cmd:S7200")
            # sleep(1)
            formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
            #print('start_', formatted_time)
            # print_redis_data(r2)
            CommonUser.__publish_message(session, channelpublish, jsonstr)
            # 发布后打印
            formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
            print('start22_', formatted_time)
            # __writeToLog('start22_')
        istart_time = datetime.now()
        itime_diff = 0
        iHas = 0
        while (len(channelx) > 0) and (itime_diff<=6):
            sleep(0.2)
            iend_time = datetime.now()
            itime_diff = iend_time - istart_time
            itime_diff = itime_diff.total_seconds()
        #print('josnendddddddd_', datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
        if (len(channelx) <= 0):
            return (conFuncErr, '', False)
        else:
            return (conFuncErr, '', True)

   except Exception as e:
        errorStr = str(e)
        __writeToLog('ts_josn_set error' + errorStr)
        return (conFuncErr, errorStr, True)

# 创建socket连接
def __createSocket(DevIP, DevPort):
	for i in range(3):
		try:
			session = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
			session.settimeout(0.5)  # 设置超时时间  5秒
			session.connect((DevIP, int(DevPort)))
			return session
		except Exception as e:
			__writeToLog('createSocket error:' + str(e))
			return None
		continue


# 将字符串发送到服务器。
def __SendstrData(session, sstr):
	try:
		sstr = sstr + '\r\n'
		encoded_message = sstr.encode('utf-8')
		session.sendall(encoded_message)
	except Exception as e:
		errorStr = str(e)
		__writeToLog('SendstrData error' + errorStr)

# 将字节串发送到服务器。
def __SendbyteData(session, byteArr):
	try:
		tcpSentMes = bytes(byteArr)
		session.sendall(tcpSentMes)
		return (1, '', False)
	except Exception as e:
		errorStr = str(e)
		__writeToLog('__SendbyteData error' + errorStr)
		return (1, '', True)


# #打开设备连接
# eg DevIP='127.0.0.1' ;  DevPort=502
def __openDevice(DevIP, DevPort):
	try:
		session = __createSocket(DevIP, DevPort)
		return session
	except Exception as e:
		errorStr = str(e)
		__writeToLog('openDevice error' + errorStr)
		return None


# 关闭设备连接
def __closeDevice(session):
	try:
		session.close()
	except Exception as e:
		errorStr = str(e)
		__writeToLog('closeDevice connect error' + errorStr)


# str 内容暂用固定str
def __set_voltage(session, str, varData):
	try:
		databyte = [0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0xff]
		errt = __SendbyteData(session, databyte)
		if errt[2] == False:
			varData['status'] = 'Success'
		else:
			varData['status'] = 'Fail'
	except Exception as e:
		errorStr = str(e)
		__writeToLog('set_voltage error' + errorStr)
		varData['status'] = 'Error'
		return None

def __set_Curr(session, str, varData):
	try:
		databyte = [0xff, 0xfe, 0x03, 0x04, 0x05, 0x06, 0x07, 0xff]
		errt = __SendbyteData(session, databyte)
		if errt[2] == False:
			varData['status'] = 'Success'
		else:
			varData['status'] = 'Fail'
	except Exception as e:
		errorStr = str(e)
		__writeToLog('set_voltage error' + errorStr)
		varData['status'] = 'Error'
		return None

def __set_test(session, str, varData):
	try:
		databyte = [0xf1, 0xf2, 0xf3, 0xf4, 0xf5, 0xf6, 0xf7, 0xf8]
		errt = __SendbyteData(session, databyte)
		if errt[2] == False:
			varData['status'] = 'Success'
		else:
			varData['status'] = 'Fail'
	except Exception as e:
		errorStr = str(e)
		__writeToLog('set_voltage error' + errorStr)
		varData['status'] = 'Error'
		return None

# json_input:
# {
#     "Commands": "set_voltage",
#     "Args": {
#       "voltage" : 100,
#       "var1": 1,
# 		"var2": bool,
#       ...
#       ...
#     },
#     "Request_Id": "uuid-1234"
#     "Timeout": 1
# }
def server_set(json_input: str, device_info_json: str, session) -> str:
    try:

        # time.sleep(2.5)
        data = {
            "Request_Id": "uuid-1234",
            "status": "success",
            "result": {"data": "..."},
            "error": None
        }

        # 具体参数等信息需要从 json_input 中的Args中根据实际情况解析出来。
        __writeToLog(json_input)
        serverStr = json.loads(json_input)
        if isDebug == 1:
            if serverStr['Command'] == 'run_device':
                __set_voltage(session,'',data)
            elif serverStr['Command'] == 'stop_device':
                __set_Curr(session,'',data)
            elif serverStr['Command'] == 'ParasSet':
                __set_test(session,'',data)
            else:
                __set_test(session, '', data)

            data['result']['data']=serverStr['Args']
            data["Request_Id"] = serverStr["Request_Id"]
            data['status'] = 'Fail'
        else:

            if serverStr['Command'] == 'Remote_device':
                errret=__Remote_link_device(None,session)
            if serverStr['Command'] == 'run_device':
                errret=__run_device(None,session)
            elif serverStr['Command'] == 'stop_device':
                errret=__stop_device(None,session)
            elif serverStr['Command'] == 'reset_device':
                errret=__reset_device(None,session)
            elif serverStr['Command'] == 'ParasSet':
                dataArgs=serverStr['Args']
                errret=__ParasSet(None,session,dataArgs)
            elif serverStr['Command'] == 'ParasSet_ProtectData':
                dataArgs=serverStr['Args']
                errret=__ParasSet_ProtectData(None,session,dataArgs)
            elif serverStr['Command'] == 'ParasSet_By_step':
                dataArgs = serverStr['Args']
                StepMode=dataArgs['StepMode']
                Value=dataArgs['Value']
                errret = __ParasSet_By_step(None,StepMode,Value,session)

            if errret[2]==True:
                data['status']='Fail'
            data['result']['data'] = ['Args']
            data["Request_Id"] = serverStr["Request_Id"]
        json_string = json.dumps(data)
        __writeToLog('json_string_'+json_string)
        return json_string
    except Exception as e:
        errorStr = str(e)
        __writeToLog('set error' + errorStr)
        data['status']='error'
        json_string = json.dumps(data)
        return json_string

# -------------------------连接设备与循环读值----------------------------

# 有些设备不需要连接设备
# 连接设备
# device_info_json:
# {
#     "DeviceType": "ZCAN_USBCANFD_200U", // 设备类型
#     "DeviceModel": "2ZZZ", // 设备型号
#     "CustomName": "can12", // 设备别名
#     "VisaInterfaceType": "VXI BACKPLANE", //
#     "ResourceAddress": "9022F352A0140DD49BB0", // 设备资源地址  "127.0.0.1:502"
#     "RedisWriteType": "1", //
#     "CodeFileName": "S7000_demo.py", // 设备代码名称
#     "IsEnabled": true // 设备启用使能
#     "DevIp": "127.0.0.1", // 删除
#  "DevPort": true // 设备Port // 删除
# }
def server_connect(device_info_json: str) -> dict:
    try:

        # time.sleep(1)

        # 连接操作
        # （如果不需要connect执行任何事情，直接ret_session=None就好）
        __writeToLog('connect__'+device_info_json)
        if isDebug==1:
            ret_session = __openDevice('127.0.0.1', 502)
        else:

            device_info_json={'DevIp':'127.0.0.1', 'DevPort':'502'}
            device_info_json = json.dumps(device_info_json)



            sdevice_info_json = json.loads(device_info_json)
            sDevIp = sdevice_info_json['DevIp']
            iDevPort = sdevice_info_json['DevPort']
            # ret_session = __openDevice('127.0.0.1', 1030)
            ret_session = __open_instrument_resource(None,sDevIp,iDevPort)

        is_success = False
        if ret_session :
            is_success=True
        __writeToLog('connect success')

        return {
            "is_success": is_success,  # 连接是否成功
            "session": ret_session,
        }
    except Exception as e:
        errorStr = str(e)
        __writeToLog('connect error' + errorStr)

# 连接设备成功后会循环调用这个函数获取值
def server_collect(device_info_json: str, session) -> dict:
    try:
        # time.sleep(0.1)

        # 示例数据，上位机读取到后写入至相应的 Redis 地址中。
        # （如果不需要collect执行任何事情，返回一个空字典即可）
        data = {
            "ErrorCode": 212,  # 故障码
            "ErrorDescription": "dwq",  # 故障描述
            "NeedReconnect": False,  # 故障是否需要重连
            "Data": {  # 实际数据（键值对形式）
                "Data1": 1,
                "Data2": 13,
                "Data3": 1123,
                "Data4": False,
                # ...
            }
        }
        # return { }
        data['Data']={}
        if isDebug <= 1:
            #__set_test(session, '', data)
            data['Data']['Time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            data['Data']['Vol']=1
            data['Data']['Cur']=2
            data['Data']['Po']=3
            data['Data']['State'] = 0
            data['Data']['FaultCode0'] = 0
            data['Data']['FaultCode1'] = 0
            data['Data']['FaultCode2'] = 0
            data['Data']['FaultCode3'] = 0
            data['Data']['FaultCode4'] = 0
            data['Data']['FaultCode5'] = 0
            data['Data']['FaultCode6'] = 0
            data['Data']['FaultCode7'] = 0
            data['Data']['FaultCode8'] = 0
            #data["NeedReconnect"]=True
        else:
            __read_data_device(None,session,data)
        print('data',data)
        return data
    except Exception as e:
        errorStr = str(e)
        __writeToLog('collect error' + errorStr)
        data['Data']={}



def server_disconnect(device_info_json: str, session) -> dict: #server_
    try:
        # 关闭连接之类的
        if isDebug == 1:
            session.close()
        else:
            ts_close_instrument_resource(session)
        is_success = True

        return {
            "is_success": is_success,  # 断开连接是否成功
        }
    except Exception as e:
        errorStr = str(e)
        __writeToLog('disconnect error ' + errorStr)


def __exsample():
    #-------测试专用-发布时屏蔽-------------------
    Client=ts_open_instrument_resource(None,'127.0.0.1',502)

    time.sleep(0.2)
    #__Remote_link_device(None,Client)
    #__run_device(None,Client)
    #time.sleep(0.2)
    #Data_Set_device('shififd',Client)
    #__read_data_device('asiufsdhf',Client)
    #read_error_device('asiufsdhf',Client)

def __exsample2():
    ret_sessionArr=server_connect('') #连接
    ret_session=ret_sessionArr['session']
    idata = server_collect('',ret_session) #状态查询
    print(idata)
    data = {
        "script": "S7200.py",
        "method": "set",
        "Commands": "run_device",#"set_voltage",
        "args": {"voltage": 100},
        "request_id": "uuid-1234",
        "timeout":1,
         "delay": 100
    }
    json_string = json.dumps(data)
    idata=set(json_string,'',ret_session)
    print(idata)
    server_disconnect('',ret_session)
#__exsample2()
def __exsample_redis():
    global ConJosnOrCommand
    global rrConn

    ConJosnOrCommand=1   #1_ts_redis, 0_ts直接控制
    session=ts_open_instrument_resource(None,'*************',502)
    #idata=ts_read_data_device(None,session)
    rrConn = ts_NewConn_Monitor(None)
    idata=ts_run_device(None,session,rrConn)
    print('idata', idata)
    #sleep(5)
    #
    #rrConn = ts_NewConn_Query(None)
    #rrConn = ts_NewConn_Monitor(None)

    idata=ts_run_device(None,session,rrConn)
    print('idata', idata)
    return
    idata=ts_stop_device(None, session,rrConn)
    print('idata', idata)
    idata=ts_reset_device(None,session,rrConn)
    print('idata', idata)
    idata=ts_ParasSet_By_step(None,0,1,session,rrConn)
    print('idata', idata)
    idata=ts_ParasSet_ProtectData(None,session,rrConn)
    print('idata', idata)
    idata=ts_ParasSet(None,session,rrConn)
    print('idata', idata)
    rrsession=ts_NewConn_Query(None)
    for i in range(3):
        idata=ts_read_data_device(None,rrsession)
        print('idata', idata)
    ts_close_instrument_resource(None,session)
#__exsample_redis()
def __exsample_SERVER():
    global ConJosnOrCommand
    global rrConn

    ConJosnOrCommand=0   #1_ts_redis, 0_ts直接控制
    session=ts_open_instrument_resource(None,'127.0.0.1',502)
    ts_ParasSet(None,session,rrConn)
    #ts_run_device(None,session,None)
    #idata=ts_read_data_device(None,session)
    #print(idata)
    sleep(3)
    ts_close_instrument_resource(None, session)
#__exsample_SERVER()
#Exception response 144 / 0





