-- third模块 - 直接使用全局变量版本
-- 可以直接使用 A, B, C，无需任何前缀

print("=== third_global_direct.lua 开始加载 ===")

-- 创建模块表
local third = {}

-- 模块自己的变量
third.X = 5000
third.Y = 6000
third.Z = 7000
third.name = "third_global_direct"

print("third模块加载，可以直接使用全局变量 A, B, C")

-- 直接使用全局变量
function third.use_global_vars()
    print("=== third模块直接使用全局变量 ===")
    
    -- 直接访问，无需前缀
    print("直接访问 A = " .. A)
    print("直接访问 B = " .. B)
    print("直接访问 C = " .. C)
    
    -- 直接计算
    local average = (A + B + C) / 3
    local variance = ((A - average)^2 + (B - average)^2 + (C - average)^2) / 3
    
    print("平均值 = " .. average)
    print("方差 = " .. variance)
    
    -- 直接修改
    print("\nthird模块修改全局变量:")
    A = A + 5
    B = B + 15
    C = C + 25
    print("修改后 A = " .. A)
    print("修改后 B = " .. B)
    print("修改后 C = " .. C)
end

-- 复杂的全局变量操作
function third.complex_global_operations()
    print("=== third模块复杂全局变量操作 ===")
    
    -- 直接使用全局变量进行复杂计算
    local ratio_AB = A / B
    local ratio_BC = B / C
    local ratio_CA = C / A
    
    print("比率 A/B = " .. ratio_AB)
    print("比率 B/C = " .. ratio_BC)
    print("比率 C/A = " .. ratio_CA)
    
    -- 根据比率调整变量
    if ratio_AB > 2 then
        print("A/B比率过高，调整A")
        A = A * 0.8
    elseif ratio_AB < 0.5 then
        print("A/B比率过低，调整A")
        A = A * 1.2
    end
    
    if ratio_BC > 2 then
        print("B/C比率过高，调整B")
        B = B * 0.8
    elseif ratio_BC < 0.5 then
        print("B/C比率过低，调整B")
        B = B * 1.2
    end
    
    print("调整后: A=" .. A .. ", B=" .. B .. ", C=" .. C)
end

-- 数学运算函数
function third.mathematical_operations()
    print("=== third模块数学运算 ===")
    
    -- 直接使用全局变量进行数学运算
    local geometric_mean = (A * B * C) ^ (1/3)
    local harmonic_mean = 3 / (1/A + 1/B + 1/C)
    local quadratic_mean = math.sqrt((A^2 + B^2 + C^2) / 3)
    
    print("几何平均数 = " .. geometric_mean)
    print("调和平均数 = " .. harmonic_mean)
    print("平方平均数 = " .. quadratic_mean)
    
    -- 三角函数运算
    local angle_A = math.atan(A / 100)
    local angle_B = math.atan(B / 100)
    local angle_C = math.atan(C / 100)
    
    print("角度A = " .. math.deg(angle_A) .. "度")
    print("角度B = " .. math.deg(angle_B) .. "度")
    print("角度C = " .. math.deg(angle_C) .. "度")
    
    return {
        geometric = geometric_mean,
        harmonic = harmonic_mean,
        quadratic = quadratic_mean
    }
end

-- 模拟物理计算
function third.physics_simulation()
    print("=== third模块物理模拟 ===")
    
    -- 将 A, B, C 当作物理量进行计算
    local velocity = A  -- 速度
    local time = B      -- 时间
    local acceleration = C  -- 加速度
    
    print("初始速度 = " .. velocity)
    print("时间 = " .. time)
    print("加速度 = " .. acceleration)
    
    -- 物理公式计算
    local distance = velocity * time + 0.5 * acceleration * time^2
    local final_velocity = velocity + acceleration * time
    local kinetic_energy = 0.5 * 1 * final_velocity^2  -- 假设质量为1
    
    print("位移 = " .. distance)
    print("最终速度 = " .. final_velocity)
    print("动能 = " .. kinetic_energy)
    
    -- 更新全局变量（模拟状态变化）
    A = final_velocity  -- 更新速度
    B = B + 1          -- 时间推进
    
    print("更新后: 速度(A)=" .. A .. ", 时间(B)=" .. B)
    
    return distance
end

-- 获取贡献值
function third.get_contribution()
    -- 使用全局变量和模块变量计算贡献
    local global_factor = (A + B + C) * 0.2
    local module_factor = (third.X + third.Y + third.Z) * 0.1
    local contribution = global_factor + module_factor
    
    print("third模块贡献: " .. contribution)
    return contribution
end

-- 与other模块的数据交换
function third.data_exchange_with_other()
    print("=== third与other数据交换 ===")
    
    if _G.other then
        print("交换前:")
        print("  全局: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
        print("  third: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
        
        -- 数据交换
        local temp_A = A
        A = other.X / 100  -- 缩放other的X到合适范围
        other.X = temp_A * 100  -- 放大A到other的范围
        
        local temp_B = B
        B = other.Y / 100
        other.Y = temp_B * 100
        
        print("交换后:")
        print("  全局: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
    else
        print("other模块未找到")
    end
end

-- 全局变量的统计分析
function third.statistical_analysis()
    print("=== 全局变量统计分析 ===")
    
    -- 基本统计
    local sum = A + B + C
    local mean = sum / 3
    local max_val = math.max(A, B, C)
    local min_val = math.min(A, B, C)
    local range = max_val - min_val
    
    print("总和 = " .. sum)
    print("平均值 = " .. mean)
    print("最大值 = " .. max_val)
    print("最小值 = " .. min_val)
    print("极差 = " .. range)
    
    -- 标准差计算
    local variance = ((A - mean)^2 + (B - mean)^2 + (C - mean)^2) / 3
    local std_dev = math.sqrt(variance)
    
    print("方差 = " .. variance)
    print("标准差 = " .. std_dev)
    
    -- 变异系数
    local cv = std_dev / mean * 100
    print("变异系数 = " .. cv .. "%")
    
    return {
        sum = sum,
        mean = mean,
        std_dev = std_dev,
        cv = cv
    }
end

-- 演示所有功能
function third.demo_all_features()
    print("\n=== third模块全功能演示 ===")
    
    print("1. 直接使用全局变量:")
    third.use_global_vars()
    
    print("\n2. 复杂全局变量操作:")
    third.complex_global_operations()
    
    print("\n3. 数学运算:")
    local math_results = third.mathematical_operations()
    
    print("\n4. 物理模拟:")
    local distance = third.physics_simulation()
    print("模拟结果: 位移 = " .. distance)
    
    print("\n5. 统计分析:")
    local stats = third.statistical_analysis()
    
    print("\n6. 数据交换:")
    third.data_exchange_with_other()
end

-- 打印模块状态
function third.print_status()
    print("=== third模块状态 ===")
    print("全局变量: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    print("模块变量: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
    print("模块名称: " .. third.name)
end

print("third_global_direct.lua 加载完成")

return third
