-- third模块 - 模块化命名版本
-- 使用 third.xxx 命名，避免与主脚本的 A, B, C 冲突

print("=== third_modular_naming.lua 开始加载 ===")

-- 创建模块表
local third = {}

-- 模块变量使用模块化命名
third.X = 5000
third.Y = 6000
third.Z = 7000
third.name = "third_modular_naming"
third.version = "1.0"

print("third模块使用模块化命名: third.X, third.Y, third.Z")

-- 模块函数
function third.print_variables()
    print("=== third模块变量 ===")
    print("third.X = " .. third.X)
    print("third.Y = " .. third.Y)
    print("third.Z = " .. third.Z)
end

function third.calculate()
    local sum = third.X + third.Y + third.Z
    local product = third.X * third.Y * third.Z
    local average = sum / 3
    
    print("third模块计算:")
    print("  sum = " .. sum)
    print("  product = " .. product)
    print("  average = " .. average)
    
    return {sum = sum, product = product, average = average}
end

-- 直接使用主脚本的全局变量 A, B, C
function third.use_main_vars()
    print("=== third模块使用主脚本的全局变量 ===")
    
    -- 直接使用 A, B, C（来自主脚本）
    print("直接访问主脚本的 A = " .. A)
    print("直接访问主脚本的 B = " .. B)
    print("直接访问主脚本的 C = " .. C)
    
    -- 与自己的变量混合计算
    local mixed_calc1 = A * third.X / 1000  -- 主脚本的A * third模块的X
    local mixed_calc2 = B + third.Y / 100   -- 主脚本的B + third模块的Y
    local mixed_calc3 = C * third.Z / 1000  -- 主脚本的C * third模块的Z
    
    print("混合计算:")
    print("  A * third.X / 1000 = " .. mixed_calc1)
    print("  B + third.Y / 100 = " .. mixed_calc2)
    print("  C * third.Z / 1000 = " .. mixed_calc3)
    
    -- 修改主脚本的变量
    print("third模块修改主脚本变量:")
    A = A + 2
    B = B + 4
    C = C + 6
    print("  修改后 A = " .. A)
    print("  修改后 B = " .. B)
    print("  修改后 C = " .. C)
end

-- 高级数学运算
function third.advanced_math_operations()
    print("=== third模块高级数学运算 ===")
    
    -- 使用主脚本变量和模块变量进行高级计算
    print("输入数据:")
    print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    print("  third模块: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
    
    -- 几何平均数
    local geo_mean_main = (A * B * C) ^ (1/3)
    local geo_mean_third = (third.X * third.Y * third.Z) ^ (1/3)
    
    print("几何平均数:")
    print("  主脚本: " .. geo_mean_main)
    print("  third模块: " .. geo_mean_third)
    
    -- 调和平均数
    local harm_mean_main = 3 / (1/A + 1/B + 1/C)
    local harm_mean_third = 3 / (1/third.X + 1/third.Y + 1/third.Z)
    
    print("调和平均数:")
    print("  主脚本: " .. harm_mean_main)
    print("  third模块: " .. harm_mean_third)
    
    -- 交叉相关性分析
    local correlation = (A * third.X + B * third.Y + C * third.Z) / 
                       (math.sqrt(A^2 + B^2 + C^2) * math.sqrt(third.X^2 + third.Y^2 + third.Z^2))
    
    print("交叉相关性: " .. correlation)
    
    return {
        geo_main = geo_mean_main,
        geo_third = geo_mean_third,
        harm_main = harm_mean_main,
        harm_third = harm_mean_third,
        correlation = correlation
    }
end

-- 与other模块的三方协作
function third.three_way_collaboration()
    print("=== 三方协作（main + other + third）===")
    
    if _G.other then
        print("协作前状态:")
        print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
        print("  third: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
        
        -- 计算各模块的权重
        local main_weight = (A + B + C) / 3
        local other_weight = (other.X + other.Y + other.Z) / 3
        local third_weight = (third.X + third.Y + third.Z) / 3
        local total_weight = main_weight + other_weight + third_weight
        
        print("权重分析:")
        print("  主脚本权重: " .. main_weight .. " (" .. string.format("%.1f", main_weight/total_weight*100) .. "%)")
        print("  other权重: " .. other_weight .. " (" .. string.format("%.1f", other_weight/total_weight*100) .. "%)")
        print("  third权重: " .. third_weight .. " (" .. string.format("%.1f", third_weight/total_weight*100) .. "%)")
        
        -- 根据权重调整变量
        if main_weight > total_weight * 0.5 then
            print("主脚本权重过高，进行平衡调整")
            A = A * 0.8
            B = B * 0.8
            C = C * 0.8
            other.X = other.X * 1.1
            third.X = third.X * 1.1
        elseif main_weight < total_weight * 0.2 then
            print("主脚本权重过低，进行提升调整")
            A = A * 1.2
            B = B * 1.2
            C = C * 1.2
        end
        
        print("调整后状态:")
        print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
        print("  third: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
        
        return total_weight
    else
        print("other模块未找到，无法进行三方协作")
        return A + B + C + third.X + third.Y + third.Z
    end
end

-- 模拟物理系统
function third.physics_simulation()
    print("=== third模块物理系统模拟 ===")
    
    -- 将变量映射到物理量
    local mass = A        -- 质量（来自主脚本）
    local velocity = B    -- 速度（来自主脚本）
    local time = C        -- 时间（来自主脚本）
    
    local force_x = third.X / 1000    -- X方向力
    local force_y = third.Y / 1000    -- Y方向力
    local force_z = third.Z / 1000    -- Z方向力
    
    print("物理参数:")
    print("  质量: " .. mass)
    print("  初始速度: " .. velocity)
    print("  时间: " .. time)
    print("  力 (X, Y, Z): " .. force_x .. ", " .. force_y .. ", " .. force_z)
    
    -- 计算加速度 (F = ma)
    local accel_x = force_x / mass
    local accel_y = force_y / mass
    local accel_z = force_z / mass
    
    print("加速度 (X, Y, Z): " .. accel_x .. ", " .. accel_y .. ", " .. accel_z)
    
    -- 计算位移 (s = vt + 0.5at²)
    local displacement_x = velocity * time + 0.5 * accel_x * time^2
    local displacement_y = velocity * time + 0.5 * accel_y * time^2
    local displacement_z = velocity * time + 0.5 * accel_z * time^2
    
    print("位移 (X, Y, Z): " .. displacement_x .. ", " .. displacement_y .. ", " .. displacement_z)
    
    -- 更新主脚本变量（模拟状态变化）
    A = mass  -- 质量不变
    B = velocity + (accel_x + accel_y + accel_z) * time / 3  -- 平均加速后的速度
    C = time + 1  -- 时间推进
    
    print("更新后的主脚本变量:")
    print("  A (质量) = " .. A)
    print("  B (速度) = " .. B)
    print("  C (时间) = " .. C)
    
    return math.sqrt(displacement_x^2 + displacement_y^2 + displacement_z^2)
end

-- 演示清晰的命名空间
function third.demonstrate_clear_naming()
    print("=== 演示清晰的命名空间 ===")
    
    print("命名规则:")
    print("  A, B, C           <- 主脚本的全局变量")
    print("  other.X, Y, Z     <- other模块的变量")
    print("  third.X, Y, Z     <- third模块的变量")
    
    print("\n优势:")
    print("  ✅ 完全避免命名冲突")
    print("  ✅ 变量来源一目了然")
    print("  ✅ 主脚本代码最简洁")
    print("  ✅ 模块间界限清晰")
    
    print("\n实际使用示例:")
    local example1 = A + other.X + third.X
    local example2 = B * other.Y / third.Y
    local example3 = C + other.Z - third.Z
    
    print("  A + other.X + third.X = " .. example1)
    print("  B * other.Y / third.Y = " .. example2)
    print("  C + other.Z - third.Z = " .. example3)
    
    print("\n代码可读性:")
    print("  每个变量的来源都很清楚")
    print("  不需要查看其他文件就能理解代码")
end

-- 获取模块状态
function third.get_status()
    return {
        module_vars = {X = third.X, Y = third.Y, Z = third.Z},
        main_vars = {A = A, B = B, C = C},
        module_name = third.name
    }
end

print("third_modular_naming.lua 加载完成")

return third
