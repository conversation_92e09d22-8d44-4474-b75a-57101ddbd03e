-- other模块 - 元表自动同步版本
-- 使用元表实现内外部变量的自动同步

print("=== other_metatable_sync.lua 开始加载 ===")

-- 创建模块表
local other = {}

-- 内部变量存储
local internal_vars = {
    A = 100,
    B = 200,
    C = 300,
    name = "other_metatable_sync",
    version = "4.1"
}

-- 创建内部环境，支持直接访问变量
local internal_env = {}

-- 设置内部环境的元表
setmetatable(internal_env, {
    __index = function(t, key)
        -- 首先检查内部变量
        if internal_vars[key] ~= nil then
            return internal_vars[key]
        end
        -- 然后检查全局环境
        return _G[key]
    end,
    
    __newindex = function(t, key, value)
        -- 设置内部变量
        internal_vars[key] = value
        -- 自动同步到模块表
        other[key] = value
        print("内部设置 " .. key .. " = " .. value .. "，已自动同步到外部")
    end
})

-- 设置模块表的元表
setmetatable(other, {
    __index = function(t, key)
        return internal_vars[key]
    end,
    
    __newindex = function(t, key, value)
        -- 外部修改时，同步到内部
        internal_vars[key] = value
        rawset(t, key, value)
        print("外部设置 " .. key .. " = " .. value .. "，已自动同步到内部")
    end
})

-- 初始化外部访问
for key, value in pairs(internal_vars) do
    other[key] = value
end

print("设置了元表自动同步系统")

-- 在内部环境中定义函数
local function define_internal_functions()
    -- 设置函数环境为内部环境
    local function internal_calculate()
        -- 直接使用 A, B, C（通过元表访问）
        local sum = A + B + C
        local product = A * B * C
        
        print("内部计算（直接访问变量）:")
        print("  A + B + C = " .. sum)
        print("  A * B * C = " .. product)
        
        return {sum = sum, product = product}
    end
    
    local function internal_modify()
        print("内部修改变量（直接访问）:")
        
        -- 直接修改，会自动同步
        A = A + 50
        B = B * 1.1
        C = C - 10
        
        print("  修改后 A = " .. A)
        print("  修改后 B = " .. B)
        print("  修改后 C = " .. C)
    end
    
    local function internal_complex_logic()
        print("内部复杂逻辑:")
        
        -- 直接使用变量进行复杂计算
        local factor = (A + B) / C
        
        if factor > 2 then
            A = A * 0.9
            B = B * 0.9
        elseif factor < 1 then
            A = A * 1.1
            B = B * 1.1
        end
        
        -- 条件逻辑
        if A > B then
            C = (A + B) / 2
        else
            C = A * B / 100
        end
        
        print("  因子 = " .. factor)
        print("  调整后 A = " .. A)
        print("  调整后 B = " .. B)
        print("  调整后 C = " .. C)
        
        return factor
    end
    
    local function internal_use_third()
        print("内部访问third模块:")
        
        if _G.third then
            -- 直接使用内部变量和third模块
            local combined = A + third.X
            print("  A + third.X = " .. combined)
            
            -- 根据third的值调整内部变量
            if third.X > 1000 then
                A = A + 100
                print("  因为third.X > 1000，增加A到 " .. A)
            end
            
            return combined
        else
            print("  third模块未找到")
            return 0
        end
    end
    
    -- 设置函数环境
    setfenv(internal_calculate, internal_env)
    setfenv(internal_modify, internal_env)
    setfenv(internal_complex_logic, internal_env)
    setfenv(internal_use_third, internal_env)
    
    return {
        calculate = internal_calculate,
        modify = internal_modify,
        complex_logic = internal_complex_logic,
        use_third = internal_use_third
    }
end

-- 获取内部函数
local internal_funcs = define_internal_functions()

-- 外部接口函数
function other.print_variables()
    print("=== other模块变量（元表同步版本）===")
    print("other.A = " .. other.A)
    print("other.B = " .. other.B)
    print("other.C = " .. other.C)
    
    print("\n内部变量状态:")
    for key, value in pairs(internal_vars) do
        if type(value) ~= "function" then
            print("  内部 " .. key .. " = " .. value)
        end
    end
end

function other.calculate()
    print("外部调用计算")
    return internal_funcs.calculate()
end

function other.modify_variables()
    print("外部调用修改")
    internal_funcs.modify()
end

function other.run_complex_logic()
    print("外部调用复杂逻辑")
    return internal_funcs.complex_logic()
end

function other.use_third_variables()
    print("外部调用访问third")
    return internal_funcs.use_third()
end

-- 演示自动同步
function other.demo_auto_sync()
    print("\n=== 演示自动同步 ===")
    
    print("1. 外部修改 other.A:")
    local old_A = other.A
    other.A = 999
    print("   外部: other.A = " .. other.A)
    print("   内部: internal_vars.A = " .. internal_vars.A)
    
    print("\n2. 内部修改（通过内部函数）:")
    internal_funcs.modify()
    print("   修改后外部: other.A = " .. other.A)
    print("   修改后外部: other.B = " .. other.B)
    
    print("\n3. 验证同步:")
    local synced = true
    for key, value in pairs(internal_vars) do
        if type(value) ~= "function" and other[key] ~= value then
            synced = false
            break
        end
    end
    print("   同步状态: " .. (synced and "已同步" or "未同步"))
end

-- 获取同步状态
function other.get_sync_status()
    local status = {}
    for key, value in pairs(internal_vars) do
        if type(value) ~= "function" then
            status[key] = {
                internal = value,
                external = other[key],
                synced = (value == other[key])
            }
        end
    end
    return status
end

-- 强制同步
function other.force_sync()
    print("强制同步内外部变量")
    for key, value in pairs(internal_vars) do
        if type(value) ~= "function" then
            other[key] = value
        end
    end
    print("同步完成")
end

print("other_metatable_sync.lua 加载完成")

return other
