-- 主Lua脚本文件 - 全局变量版本
-- 设置全局变量（任何地方都可以直接使用）
A = 10
B = 20
C = 30
main_name = "main_script"

print("主脚本中设置了全局变量 A, B, C")

-- 加载其他模块
local other = dofile("other_script.lua")
local third = dofile("third_script.lua")

-- add函数：计算两个数的和
function add(x, y)
    return x + y
end

-- 模块化的load函数
function load_module(script_path)
    print("正在加载模块: " .. script_path)

    local success, result = pcall(dofile, script_path)

    if success then
        print("模块加载成功")
        return result
    else
        print("模块加载失败: " .. tostring(result))
        return nil
    end
end

-- 演示模块化变量访问的主函数
function demo_modular_access()
    print("\n=== 模块化变量访问演示 ===")

    -- 1. 在main中访问other模块的变量
    print("\n1. 在main中访问other模块的变量:")
    print("other.A = " .. other.A)
    print("other.B = " .. other.B)
    print("other.C = " .. other.C)

    -- 2. 在main中修改other模块的变量
    print("\n2. 在main中修改other模块的变量:")
    local old_A = other.A
    other.A = 999
    print("修改 other.A 从 " .. old_A .. " 到 " .. other.A)

    -- 3. 在main中访问third模块的变量
    print("\n3. 在main中访问third模块的变量:")
    print("third.X = " .. third.X)
    print("third.Y = " .. third.Y)
    print("third.Z = " .. third.Z)

    -- 4. 在main中修改third模块的变量
    print("\n4. 在main中修改third模块的变量:")
    local old_X = third.X
    third.X = 5555
    print("修改 third.X 从 " .. old_X .. " 到 " .. third.X)

    -- 5. 让third模块与other模块交互
    print("\n5. 让third模块与other模块交互:")
    third.interact_with_other(other)

    -- 6. 批量操作演示
    print("\n6. 批量操作演示:")
    third.batch_operation(other)

    -- 7. 多模块交互
    print("\n7. 多模块交互:")
    local modules = {
        other = other,
        third = third
    }
    third.multi_module_interaction(modules)

    -- 8. 复杂的跨模块计算
    print("\n8. 复杂的跨模块计算:")
    local result = main.A + other.B + third.Z
    print("main.A + other.B + third.Z = " .. main.A .. " + " .. other.B .. " + " .. third.Z .. " = " .. result)
end

-- 执行演示
demo_modular_access()
