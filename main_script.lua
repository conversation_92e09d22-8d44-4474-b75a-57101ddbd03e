-- 主Lua脚本文件
-- 设置全局变量
A = 10
B = 20
C = 30

-- add函数：计算两个数的和
function add(x, y)
    return x + y
end

-- load函数：调用另一个lua脚本
function load(script_path)
    print("正在加载脚本: " .. script_path)
    
    -- 使用dofile加载并执行另一个lua脚本
    local success, result = pcall(dofile, script_path)
    
    if success then
        print("脚本加载成功")
        return result
    else
        print("脚本加载失败: " .. tostring(result))
        return nil
    end
end

-- 主函数示例
function main()
    print("主脚本开始执行")
    print("全局变量 A = " .. A)
    print("全局变量 B = " .. B)
    print("全局变量 C = " .. C)
    
    -- 测试add函数
    local sum = add(A, B)
    print("A + B = " .. sum)
    
    -- 调用另一个脚本
    load("other_script.lua")
end

-- 如果直接运行此脚本，则执行main函数
if arg and arg[0] == "main_script.lua" then
    main()
end
