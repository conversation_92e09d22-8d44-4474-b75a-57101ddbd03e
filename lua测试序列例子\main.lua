-- 主脚本 - lua测试序列例子
-- 使用简单的全局变量 A, B, C
-- 其他模块使用模块化命名避免冲突

print("=== Lua测试序列 - 主脚本 ===\n")

-- 设置主脚本的全局变量（最简洁的方式）
A = 10
B = 20
C = 30

print("主脚本设置全局变量:")
print("A = " .. A)
print("B = " .. B)
print("C = " .. C)

-- 加载其他模块
print("\n加载模块...")
local other = dofile("lua测试序列例子/other.lua")
local third = dofile("lua测试序列例子/third.lua")

print("✅ 所有模块加载完成")

-- 主脚本的核心函数
function main_calculate()
    print("\n=== 主脚本计算函数 ===")
    
    -- 直接使用全局变量，代码最简洁
    local sum = A + B + C
    local product = A * B * C
    local average = sum / 3
    
    print("基础计算:")
    print("  A + B + C = " .. sum)
    print("  A * B * C = " .. product)
    print("  平均值 = " .. average)
    
    return {sum = sum, product = product, average = average}
end

function main_process()
    print("\n=== 主脚本处理流程 ===")
    
    -- 简洁的条件逻辑
    if A > B then
        C = C + (A - B)
        print("A > B，调整 C = " .. C)
    elseif A < B then
        C = C + (B - A)
        print("A < B，调整 C = " .. C)
    else
        C = C + 1
        print("A = B，微调 C = " .. C)
    end
    
    -- 简洁的循环操作
    print("执行优化循环:")
    for i = 1, 3 do
        A = A + i
        B = B + i * 2
        print("  第" .. i .. "次: A=" .. A .. ", B=" .. B)
    end
    
    return A + B + C
end

function main_interact_with_modules()
    print("\n=== 主脚本与模块交互 ===")
    
    -- 访问其他模块的变量（清晰的命名空间）
    print("访问模块变量:")
    print("  other.X = " .. other.X)
    print("  other.Y = " .. other.Y)
    print("  third.X = " .. third.X)
    print("  third.Y = " .. third.Y)
    
    -- 跨模块计算（来源清晰）
    local cross_calc1 = A + other.X
    local cross_calc2 = B + third.Y
    local cross_calc3 = C + other.Z + third.Z
    
    print("跨模块计算:")
    print("  A + other.X = " .. cross_calc1)
    print("  B + third.Y = " .. cross_calc2)
    print("  C + other.Z + third.Z = " .. cross_calc3)
    
    -- 调用模块函数
    print("调用模块函数:")
    other.use_main_vars()
    third.use_main_vars()
    
    return cross_calc1 + cross_calc2 + cross_calc3
end

function main_complex_workflow()
    print("\n=== 主脚本复杂工作流 ===")
    
    -- 阶段1：初始化
    print("阶段1: 数据初始化")
    local init_sum = A + B + C
    print("  初始总和: " .. init_sum)
    
    -- 阶段2：模块协作
    print("阶段2: 模块协作")
    local collab_result = other.collaborate_with_third()
    print("  协作结果: " .. collab_result)
    
    -- 阶段3：数据处理
    print("阶段3: 数据处理")
    local process_result = main_process()
    print("  处理结果: " .. process_result)
    
    -- 阶段4：最终计算
    print("阶段4: 最终计算")
    local final_result = A * other.X / 1000 + B * third.Y / 1000 + C
    print("  最终结果: " .. final_result)
    
    return final_result
end

-- 批量操作所有变量
function main_batch_operations()
    print("\n=== 批量操作演示 ===")
    
    print("操作前状态:")
    print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
    print("  third: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
    
    -- 主脚本变量操作（最简洁）
    A = A + 10
    B = B * 1.1
    C = C + 5
    
    -- 模块变量操作（通过模块接口）
    other.X = other.X + 100
    other.Y = other.Y * 1.05
    third.X = third.X + 500
    third.Z = third.Z * 1.02
    
    print("操作后状态:")
    print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
    print("  third: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
end

-- 主执行流程
function main()
    print("\n" .. "="*50)
    print("开始执行主流程")
    print("="*50)
    
    -- 1. 基础计算
    local calc_result = main_calculate()
    
    -- 2. 模块交互
    local interact_result = main_interact_with_modules()
    
    -- 3. 复杂工作流
    local workflow_result = main_complex_workflow()
    
    -- 4. 批量操作
    main_batch_operations()
    
    -- 5. 最终状态报告
    print("\n=== 最终状态报告 ===")
    print("主脚本变量: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    print("计算结果: " .. calc_result.sum)
    print("交互结果: " .. interact_result)
    print("工作流结果: " .. workflow_result)
    
    print("\n=== 测试序列完成 ===")
    print("✅ 主脚本使用简单全局变量: A, B, C")
    print("✅ other模块使用: other.X, other.Y, other.Z")
    print("✅ third模块使用: third.X, third.Y, third.Z")
    print("✅ 完全避免命名冲突")
    print("✅ 代码简洁清晰")
    
    return {
        calc = calc_result,
        interact = interact_result,
        workflow = workflow_result
    }
end

-- 如果直接运行此脚本，执行主流程
if arg and arg[0] and arg[0]:match("main%.lua$") then
    main()
end

-- 导出主函数供外部调用
return {
    main = main,
    calculate = main_calculate,
    process = main_process,
    interact = main_interact_with_modules,
    workflow = main_complex_workflow,
    batch_ops = main_batch_operations
}
