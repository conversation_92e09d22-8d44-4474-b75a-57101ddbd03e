# Lua模块内外部访问分离方案

## 需求分析

您希望实现：
- **外部访问**：`other.xxxvar` 形式
- **内部访问**：直接用 `xxxvar` 形式

这是一个很实用的模块设计模式，可以让内部代码更简洁，同时保持外部接口的规范性。

## 三种解决方案

### 方案1：局部变量 + 模块表引用

**原理**：使用局部变量存储数据，手动同步到模块表

```lua
local other = {}

-- 内部使用局部变量
local A = 100
local B = 200
local C = 300

-- 映射到模块表（供外部访问）
other.A = A
other.B = B
other.C = C

-- 内部函数直接使用局部变量
local function internal_calculate()
    local sum = A + B + C  -- 直接使用 A, B, C
    return sum
end

-- 修改时需要手动同步
local function internal_modify(new_A)
    A = new_A
    other.A = A  -- 手动同步到外部
end

return other
```

**优点**：
- ✅ 内部代码简洁（直接用 `A`）
- ✅ 外部接口规范（`other.A`）
- ✅ 精确控制同步时机
- ✅ 性能好

**缺点**：
- ⚠️ 需要手动同步
- ⚠️ 容易忘记同步

### 方案2：元表自动同步

**原理**：使用元表实现内外部变量的自动同步

```lua
local other = {}
local internal_vars = {A = 100, B = 200, C = 300}

-- 创建内部环境
local internal_env = {}
setmetatable(internal_env, {
    __index = function(t, key)
        return internal_vars[key]
    end,
    __newindex = function(t, key, value)
        internal_vars[key] = value
        other[key] = value  -- 自动同步
    end
})

-- 设置模块表元表
setmetatable(other, {
    __index = function(t, key)
        return internal_vars[key]
    end,
    __newindex = function(t, key, value)
        internal_vars[key] = value  -- 自动同步
    end
})

-- 内部函数使用特殊环境
local function internal_calculate()
    local sum = A + B + C  -- 通过元表访问
    return sum
end
setfenv(internal_calculate, internal_env)
```

**优点**：
- ✅ 自动同步
- ✅ 内部代码简洁
- ✅ 外部接口规范
- ✅ 功能强大

**缺点**：
- ⚠️ 实现复杂
- ⚠️ 性能开销
- ⚠️ 调试困难

### 方案3：环境变量（推荐）

**原理**：创建模块私有环境，使用 `setfenv` 设置函数环境

```lua
local other = {}

-- 创建模块私有环境
local module_env = {
    A = 100,
    B = 200,
    C = 300,
    -- 继承必要的全局函数
    print = print,
    _G = _G
}

-- 同步函数
local function sync_to_module()
    other.A = module_env.A
    other.B = module_env.B
    other.C = module_env.C
end

-- 内部函数
local function internal_calculate()
    local sum = A + B + C  -- 直接使用变量名
    sync_to_module()  -- 同步到外部
    return sum
end

-- 设置函数环境
setfenv(internal_calculate, module_env)

return other
```

**优点**：
- ✅ 实现简洁
- ✅ 内部代码最清晰
- ✅ 性能良好
- ✅ 易于理解和维护

**缺点**：
- ⚠️ 需要手动同步
- ⚠️ 需要继承全局函数

## 使用示例

### 外部使用（所有方案相同）

```lua
local other = dofile("other_script.lua")

-- 外部访问
print("other.A = " .. other.A)
print("other.B = " .. other.B)

-- 外部修改
other.A = 555
other.B = 666

-- 调用模块函数
local result = other.calculate()
```

### 内部实现对比

```lua
-- 方案1：局部变量
local function internal_logic()
    if A > 100 then        -- 直接使用 A
        B = B * 1.1        -- 直接使用 B
        other.B = B        -- 手动同步
    end
end

-- 方案2：元表
local function internal_logic()
    if A > 100 then        -- 通过元表访问 A
        B = B * 1.1        -- 自动同步 B
    end
end
setfenv(internal_logic, internal_env)

-- 方案3：环境变量
local function internal_logic()
    if A > 100 then        -- 直接使用 A
        B = B * 1.1        -- 直接使用 B
    end
    sync_to_module()       -- 手动同步
end
setfenv(internal_logic, module_env)
```

## 性能对比

| 方案 | 访问速度 | 内存占用 | 同步开销 |
|------|----------|----------|----------|
| 方案1 | 最快 | 最少 | 无（手动） |
| 方案2 | 慢 | 最多 | 自动 |
| 方案3 | 快 | 少 | 无（手动） |

## 推荐选择

### 🏆 推荐：方案3（环境变量）

**适用场景**：
- 内部逻辑复杂
- 需要频繁使用变量
- 外部接口相对简单

**选择理由**：
1. **代码最清晰**：内部直接用 `A`，外部用 `other.A`
2. **实现简洁**：不需要复杂的元表逻辑
3. **性能良好**：直接变量访问，无元表开销
4. **易于维护**：逻辑简单，容易理解

### 备选方案

- **方案1**：适合需要精确控制同步时机的场景
- **方案2**：适合需要频繁内外部交互且不想手动同步的场景

## 实际应用建议

### 推荐的模块结构

```lua
-- other_module.lua
local other = {}

-- 模块私有环境
local env = {
    A = 100,
    B = 200,
    C = 300,
    -- 必要的全局函数
    print = print,
    math = math,
    _G = _G
}

-- 同步函数
local function sync()
    other.A = env.A
    other.B = env.B
    other.C = env.C
end

-- 内部函数（使用环境）
local function internal_work()
    -- 直接使用 A, B, C
    local result = A + B * C
    
    if result > 1000 then
        A = A * 0.9
        B = B * 0.9
    end
    
    sync()  -- 同步到外部
    return result
end

setfenv(internal_work, env)

-- 外部接口
function other.do_work()
    return internal_work()
end

-- 初始同步
sync()

return other
```

这种方案完美解决了您的需求：
- ✅ 外部使用 `other.A`
- ✅ 内部直接使用 `A`
- ✅ 代码简洁清晰
- ✅ 性能优秀
