-- 综合演示：多种模块化变量访问方案

print("=== Lua模块化变量访问综合演示 ===\n")

-- 方案1：基础模块系统（返回表）
print("1. 基础模块系统演示:")
print("加载模块...")

-- 加载模块
local other = dofile("other_script.lua")
local third = dofile("third_script.lua")

print("\n基础访问演示:")
print("other.A = " .. other.A)
print("other.B = " .. other.B)
print("third.X = " .. third.X)

-- 修改变量
print("\n修改变量:")
other.A = 555
third.X = 777
print("修改后 other.A = " .. other.A)
print("修改后 third.X = " .. third.X)

-- 跨模块交互
print("\n跨模块交互:")
third.interact_with_other(other)

-- 方案2：高级模块系统
print("\n\n2. 高级模块系统演示:")
local ModuleSystem = require("module_system")

-- 创建模块
local advanced_other = ModuleSystem.create_module("advanced_other", {
    A = 100, B = 200, C = 300, name = "高级other模块"
})

local advanced_third = ModuleSystem.create_module("advanced_third", {
    X = 1000, Y = 2000, Z = 3000, name = "高级third模块"
})

print("\n高级模块访问:")
print("advanced_other.A = " .. advanced_other.A)  -- 会触发监控
advanced_other.B = 999  -- 会记录修改

print("\n模块信息:")
ModuleSystem.list_modules()

print("\n跨模块操作:")
ModuleSystem.copy_var("advanced_other", "A", "advanced_third", "copied_A")
ModuleSystem.sync_vars("advanced_other", "B", "advanced_third", "Y")

print("\n访问日志:")
ModuleSystem.print_access_log(5)

-- 方案3：代理模块系统
print("\n\n3. 代理模块系统演示:")
local ProxyModules = require("proxy_modules")

-- 创建智能模块
local proxy_other = ProxyModules.create_smart_module("proxy_other", {
    A = 150, B = 250, C = 350, name = "代理other模块"
})

local proxy_third = ProxyModules.create_smart_module("proxy_third", {
    X = 1500, Y = 2500, Z = 3500, name = "代理third模块"
})

print("\n代理模块访问:")
print("proxy_other.A = " .. proxy_other.A)
print("proxy_third.X = " .. proxy_third.X)

-- 使用特殊方法进行跨模块操作
print("\n跨模块操作:")
proxy_third._set_to("proxy_other", "A", 888)
local copied_value = proxy_third._get_from("proxy_other", "A")
print("从proxy_other复制的A值: " .. copied_value)

-- 批量操作
print("\n批量跨模块操作:")
ProxyModules.batch_cross_module_ops({
    {type = "copy", from = "proxy_other.B", to = "proxy_third.copied_B"},
    {type = "copy", from = "proxy_third.Z", to = "proxy_other.copied_Z"}
})

print("\n所有代理模块状态:")
ProxyModules.print_all_modules()

-- 方案4：全局访问器
print("\n\n4. 全局访问器演示:")
local global_modules = ProxyModules.global_accessor()

print("通过全局访问器访问:")
print("global_modules.proxy_other.A = " .. global_modules.proxy_other.A)
print("global_modules.proxy_third.X = " .. global_modules.proxy_third.X)

-- 方案5：混合使用演示
print("\n\n5. 混合使用演示:")

-- 创建一个混合模块，可以访问所有其他模块
local mixed_module = {
    name = "混合模块",
    local_var = 42
}

-- 添加访问其他模块的方法
function mixed_module.access_all()
    print("=== 混合模块访问所有其他模块 ===")
    
    -- 访问基础模块
    print("基础模块 other.A = " .. other.A)
    print("基础模块 third.X = " .. third.X)
    
    -- 访问高级模块
    print("高级模块 advanced_other.B = " .. advanced_other.B)
    print("高级模块 advanced_third.Y = " .. advanced_third.Y)
    
    -- 访问代理模块
    print("代理模块 proxy_other.C = " .. proxy_other.C)
    print("代理模块 proxy_third.Z = " .. proxy_third.Z)
    
    -- 进行复杂计算
    local total = other.A + third.X + advanced_other.B + advanced_third.Y + 
                  proxy_other.C + proxy_third.Z + mixed_module.local_var
    
    print("所有模块变量总和 = " .. total)
    
    return total
end

-- 执行混合访问
local total_result = mixed_module.access_all()

-- 总结
print("\n\n=== 总结 ===")
print("演示了5种模块化变量访问方案:")
print("1. 基础模块系统 - 简单直接，返回表结构")
print("2. 高级模块系统 - 带监控和日志功能")
print("3. 代理模块系统 - 透明的跨模块访问")
print("4. 全局访问器 - 统一的模块访问接口")
print("5. 混合使用 - 结合多种方案的优势")

print("\n推荐使用方案:")
print("- 简单项目：方案1（基础模块系统）")
print("- 需要监控：方案2（高级模块系统）")
print("- 复杂交互：方案3（代理模块系统）")
print("- 大型项目：方案4+5（全局访问器+混合使用）")

print("\n演示完成！总计算结果: " .. total_result)
