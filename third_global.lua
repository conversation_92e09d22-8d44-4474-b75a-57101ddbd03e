-- third模块 - 全局访问版本
-- 可以直接访问全局注册的other模块

local third = {}

-- 模块变量
third.X = 1000
third.Y = 2000
third.Z = 3000
third.name = "third_global"
third.version = "2.0"

-- 模块函数
function third.print_variables()
    print("=== third_global模块变量 ===")
    print("third.X = " .. third.X)
    print("third.Y = " .. third.Y)
    print("third.Z = " .. third.Z)
end

function third.calculate()
    local sum = third.X + third.Y + third.Z
    local product = third.X * third.Y * third.Z
    local average = sum / 3
    
    print("third模块计算结果:")
    print("sum = " .. sum)
    print("product = " .. product)
    print("average = " .. average)
    
    return {sum = sum, product = product, average = average}
end

-- 直接访问other模块的函数（无需导入）
function third.use_other_variables()
    print("=== third模块访问other模块 ===")
    
    -- 方法1：通过全局MODULES访问
    if _G.MODULES and _G.MODULES.other then
        local other = _G.MODULES.other
        print("通过MODULES访问 other.A = " .. other.A)
        print("通过MODULES访问 other.B = " .. other.B)
        
        -- 修改other模块的变量
        other.A = other.A + 25
        print("修改other.A为: " .. other.A)
        
        -- 调用other模块的函数
        if other.calculate then
            print("调用other模块的计算函数:")
            other.calculate()
        end
    end
    
    -- 方法2：通过全局变量直接访问
    if _G.other then
        print("通过全局变量访问 other.C = " .. _G.other.C)
    end
    
    -- 方法3：使用跨模块访问方法
    if third.get_from then
        local other_B = third.get_from("other", "B")
        print("通过get_from访问 other.B = " .. tostring(other_B))
        
        -- 设置other模块的变量
        third.set_to("other", "C", 7777)
    end
end

-- 复杂的跨模块操作
function third.complex_interaction()
    print("=== third模块复杂跨模块操作 ===")
    
    if not _G.MODULES or not _G.MODULES.other then
        print("other模块未找到")
        return
    end
    
    local other = _G.MODULES.other
    
    -- 1. 数据同步
    print("1. 数据同步:")
    local sync_ratio = 0.1
    other.A = math.floor(third.X * sync_ratio)
    other.B = math.floor(third.Y * sync_ratio)
    other.C = math.floor(third.Z * sync_ratio)
    
    print("同步完成: other变量已根据third变量调整")
    
    -- 2. 联合计算
    print("2. 联合计算:")
    local other_sum = other.A + other.B + other.C
    local third_sum = third.X + third.Y + third.Z
    local total = other_sum + third_sum
    
    print("other模块总和: " .. other_sum)
    print("third模块总和: " .. third_sum)
    print("联合总和: " .. total)
    
    -- 3. 条件操作
    print("3. 条件操作:")
    if total > 5000 then
        print("总和超过5000，执行特殊操作")
        third.X = third.X * 1.1
        other.A = other.A * 1.1
        print("变量已调整")
    end
    
    return total
end

-- 监听来自其他模块的事件
function third.setup_event_listeners()
    if _G.on_event then
        -- 监听other模块的变量变化
        _G.on_event("variable_changed", function(data, sender)
            if sender == "other" then
                print("third模块收到other模块的变量变化通知: " .. data.var .. " = " .. data.value)
                
                -- 根据other的变化调整自己
                if data.var == "A" and data.value > 500 then
                    third.X = third.X + 100
                    print("third.X 自动调整为: " .. third.X)
                    
                    -- 发送自己的变化通知
                    third.notify_variable_change("X", third.X)
                end
            end
        end)
        
        -- 监听演示事件
        _G.on_event("demo_event", function(data, sender)
            print("third模块收到来自 " .. sender .. " 的演示事件: " .. data.message)
        end)
        
        -- 监听计算结果
        _G.on_event("calculate_result", function(data, sender)
            print("third模块收到来自 " .. sender .. " 的计算结果:")
            for k, v in pairs(data.result) do
                print("  " .. k .. " = " .. v)
            end
        end)
    end
end

-- 发送变量变化通知
function third.notify_variable_change(var_name, new_value)
    if third.emit then
        third.emit("variable_changed", {
            var = var_name,
            value = new_value
        })
    end
end

-- 请求其他模块进行计算
function third.request_calculation_from_others()
    print("third模块请求其他模块进行计算")
    if third.emit then
        third.emit("calculate_request", {
            requester = "third",
            timestamp = os.time()
        })
    end
end

-- 批量操作多个模块
function third.batch_operations()
    print("=== third模块批量操作 ===")
    
    if not _G.MODULES then
        print("全局模块系统未初始化")
        return
    end
    
    -- 遍历所有模块
    for name, module in pairs(_G.MODULES) do
        if name ~= "third" then  -- 不操作自己
            print("操作模块: " .. name)
            
            -- 如果模块有print_variables函数，调用它
            if module.print_variables then
                module.print_variables()
            end
            
            -- 如果模块有A变量，增加它
            if module.A then
                module.A = module.A + 1
                print("增加 " .. name .. ".A 到 " .. module.A)
            end
        end
    end
end

-- 模块初始化函数
function third.init()
    print("third_global模块正在初始化...")
    third.setup_event_listeners()
    print("third_global模块初始化完成")
end

-- 演示函数
function third.demo_global_access()
    print("\n=== third模块全局访问演示 ===")
    
    print("1. 打印自己的变量:")
    third.print_variables()
    
    print("\n2. 访问other模块:")
    third.use_other_variables()
    
    print("\n3. 复杂跨模块操作:")
    third.complex_interaction()
    
    print("\n4. 批量操作所有模块:")
    third.batch_operations()
    
    print("\n5. 请求其他模块计算:")
    third.request_calculation_from_others()
end

print("third_global.lua 模块已加载")

return third
