-- 高级模块系统
-- 支持模块间的变量访问和监控

local ModuleSystem = {}

-- 存储所有注册的模块
local registered_modules = {}

-- 模块访问日志
local access_log = {}

-- 创建一个新模块
function ModuleSystem.create_module(name, initial_vars)
    local module = {
        _name = name,
        _vars = initial_vars or {},
        _functions = {},
        _access_count = 0
    }
    
    -- 设置元表来监控变量访问
    local mt = {
        __index = function(t, key)
            -- 记录访问
            module._access_count = module._access_count + 1
            table.insert(access_log, {
                module = name,
                action = "read",
                key = key,
                value = module._vars[key],
                timestamp = os.time()
            })
            
            print("模块 " .. name .. " 读取变量: " .. key .. " = " .. tostring(module._vars[key]))
            
            -- 返回变量值或函数
            return module._vars[key] or module._functions[key]
        end,
        
        __newindex = function(t, key, value)
            -- 记录修改
            local old_value = module._vars[key]
            module._vars[key] = value
            
            table.insert(access_log, {
                module = name,
                action = "write",
                key = key,
                old_value = old_value,
                new_value = value,
                timestamp = os.time()
            })
            
            print("模块 " .. name .. " 设置变量: " .. key .. " = " .. tostring(value) .. 
                  (old_value and (" (原值: " .. tostring(old_value) .. ")") or ""))
        end
    }
    
    setmetatable(module, mt)
    
    -- 添加模块方法
    module._functions.get_all_vars = function()
        return module._vars
    end
    
    module._functions.set_vars = function(vars)
        for k, v in pairs(vars) do
            module[k] = v
        end
    end
    
    module._functions.print_vars = function()
        print("=== 模块 " .. name .. " 的变量 ===")
        for k, v in pairs(module._vars) do
            print(k .. " = " .. tostring(v))
        end
    end
    
    module._functions.get_info = function()
        return {
            name = name,
            var_count = 0,
            access_count = module._access_count
        }
    end
    
    -- 计算变量数量
    local var_count = 0
    for _ in pairs(module._vars) do
        var_count = var_count + 1
    end
    module._functions.get_info = function()
        return {
            name = name,
            var_count = var_count,
            access_count = module._access_count
        }
    end
    
    -- 注册模块
    registered_modules[name] = module
    
    print("模块 " .. name .. " 已创建并注册")
    return module
end

-- 获取模块
function ModuleSystem.get_module(name)
    return registered_modules[name]
end

-- 列出所有模块
function ModuleSystem.list_modules()
    print("=== 已注册的模块 ===")
    for name, module in pairs(registered_modules) do
        local info = module._functions.get_info()
        print(name .. ": " .. info.var_count .. " 个变量, " .. info.access_count .. " 次访问")
    end
end

-- 跨模块变量复制
function ModuleSystem.copy_var(from_module, from_key, to_module, to_key)
    to_key = to_key or from_key
    
    if registered_modules[from_module] and registered_modules[to_module] then
        local value = registered_modules[from_module][from_key]
        registered_modules[to_module][to_key] = value
        print("复制变量: " .. from_module .. "." .. from_key .. " -> " .. to_module .. "." .. to_key)
    else
        print("错误: 模块不存在")
    end
end

-- 同步变量（双向）
function ModuleSystem.sync_vars(module1, key1, module2, key2)
    key2 = key2 or key1
    
    if registered_modules[module1] and registered_modules[module2] then
        local value1 = registered_modules[module1][key1]
        local value2 = registered_modules[module2][key2]
        
        -- 使用较新的值（这里简单地使用module1的值）
        registered_modules[module2][key2] = value1
        print("同步变量: " .. module1 .. "." .. key1 .. " <-> " .. module2 .. "." .. key2)
    end
end

-- 打印访问日志
function ModuleSystem.print_access_log(limit)
    limit = limit or 10
    print("=== 最近 " .. limit .. " 次访问记录 ===")
    
    local count = 0
    for i = #access_log, math.max(1, #access_log - limit + 1), -1 do
        local log = access_log[i]
        print(string.format("%s: %s.%s %s %s", 
            os.date("%H:%M:%S", log.timestamp),
            log.module,
            log.key,
            log.action,
            tostring(log.value or log.new_value)))
        count = count + 1
        if count >= limit then break end
    end
end

-- 清除访问日志
function ModuleSystem.clear_log()
    access_log = {}
    print("访问日志已清除")
end

-- 模块间批量操作
function ModuleSystem.batch_operation(operations)
    print("=== 执行批量操作 ===")
    for i, op in ipairs(operations) do
        if op.type == "copy" then
            ModuleSystem.copy_var(op.from_module, op.from_key, op.to_module, op.to_key)
        elseif op.type == "set" then
            if registered_modules[op.module] then
                registered_modules[op.module][op.key] = op.value
            end
        elseif op.type == "sync" then
            ModuleSystem.sync_vars(op.module1, op.key1, op.module2, op.key2)
        end
    end
end

return ModuleSystem
