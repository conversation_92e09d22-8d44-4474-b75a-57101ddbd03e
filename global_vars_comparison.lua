-- 全局变量方案对比演示

print("=== 全局变量方案对比演示 ===\n")

-- 清理环境
for k, v in pairs(_G) do
    if type(k) == "string" and (k:match("^[ABC]$") or k:match("^MAIN_") or k:match("^OTHER_") or k:match("^THIRD_")) then
        _G[k] = nil
    end
end

print("1. 方案对比：模块表 vs 全局变量")
print("=" * 50)

-- 原始方案：模块表
print("\n原始方案（模块表）:")
print("代码: local main = {}; main.A = 10")
print("访问: main.A")
print("优点: 有命名空间，避免冲突")
print("缺点: 需要前缀 main.A")

-- 全局变量方案
print("\n全局变量方案:")
print("代码: A = 10")
print("访问: A")
print("优点: 无需前缀，直接使用")
print("缺点: 可能命名冲突")

print("\n\n2. 实际效果对比")
print("=" * 50)

-- 测试模块表方案
print("\n测试模块表方案:")
local main_module = {A = 10, B = 20, C = 30}
local other_module = {A = 100, B = 200, C = 300}

print("设置: main_module.A = 10, other_module.A = 100")
print("访问: main_module.A = " .. main_module.A)
print("访问: other_module.A = " .. other_module.A)
print("计算: main_module.A + other_module.A = " .. (main_module.A + other_module.A))

-- 测试全局变量方案
print("\n测试全局变量方案:")
A = 10
B = 20
C = 30
OTHER_A = 100
OTHER_B = 200
OTHER_C = 300

print("设置: A = 10, OTHER_A = 100")
print("访问: A = " .. A)
print("访问: OTHER_A = " .. OTHER_A)
print("计算: A + OTHER_A = " .. (A + OTHER_A))

print("\n\n3. 代码简洁度对比")
print("=" * 50)

-- 模块表方案的代码
print("\n模块表方案的函数:")
local function module_calculate()
    local sum = main_module.A + main_module.B + main_module.C
    local product = main_module.A * main_module.B * main_module.C
    
    if main_module.A > main_module.B then
        main_module.C = main_module.C + (main_module.A - main_module.B)
    end
    
    return sum, product
end

local sum1, product1 = module_calculate()
print("模块表计算结果: sum=" .. sum1 .. ", product=" .. product1)

-- 全局变量方案的代码
print("\n全局变量方案的函数:")
local function global_calculate()
    local sum = A + B + C
    local product = A * B * C
    
    if A > B then
        C = C + (A - B)
    end
    
    return sum, product
end

local sum2, product2 = global_calculate()
print("全局变量计算结果: sum=" .. sum2 .. ", product=" .. product2)

print("\n代码行数对比:")
print("模块表方案: 需要重复写 main_module. 前缀")
print("全局变量方案: 直接使用变量名，代码更简洁")

print("\n\n4. 跨模块访问对比")
print("=" * 50)

-- 模块表方案的跨模块访问
print("\n模块表方案:")
print("需要传递模块引用:")
print("function other_func(main_mod, other_mod)")
print("  return main_mod.A + other_mod.A")
print("end")

local function cross_module_calc(main_mod, other_mod)
    return main_mod.A + other_mod.A
end

local result1 = cross_module_calc(main_module, other_module)
print("跨模块计算结果: " .. result1)

-- 全局变量方案的跨模块访问
print("\n全局变量方案:")
print("直接访问，无需传递:")
print("function cross_calc()")
print("  return A + OTHER_A")
print("end")

local function cross_calc()
    return A + OTHER_A
end

local result2 = cross_calc()
print("跨模块计算结果: " .. result2)

print("\n\n5. 命名冲突处理")
print("=" * 50)

print("\n问题演示:")
-- 模拟命名冲突
A = 10
print("设置 A = 10")

-- 如果另一个模块也设置了A
A = 999  -- 被覆盖了！
print("另一个模块设置 A = 999")
print("现在 A = " .. A .. " (原值被覆盖)")

print("\n解决方案1: 使用前缀")
MAIN_A = 10
OTHER_A = 999
print("MAIN_A = " .. MAIN_A)
print("OTHER_A = " .. OTHER_A)
print("无冲突！")

print("\n解决方案2: 创建别名")
A = MAIN_A  -- 别名指向主脚本的A
print("A = " .. A .. " (别名指向 MAIN_A)")

print("\n\n6. 性能对比")
print("=" * 50)

-- 性能测试
local iterations = 100000

-- 测试模块表访问
local start_time = os.clock()
for i = 1, iterations do
    local _ = main_module.A + main_module.B + main_module.C
end
local module_time = os.clock() - start_time

-- 测试全局变量访问
start_time = os.clock()
for i = 1, iterations do
    local _ = A + B + C
end
local global_time = os.clock() - start_time

print("性能测试 (" .. iterations .. " 次迭代):")
print("模块表访问: " .. string.format("%.4f", module_time) .. " 秒")
print("全局变量访问: " .. string.format("%.4f", global_time) .. " 秒")
print("性能提升: " .. string.format("%.1f", module_time / global_time) .. " 倍")

print("\n\n7. 推荐方案")
print("=" * 50)

print("\n方案选择指南:")

print("\n✅ 推荐：带前缀的全局变量")
print("代码示例:")
print("  MAIN_A = 10")
print("  OTHER_A = 100")
print("  A = MAIN_A  -- 创建别名")
print("")
print("优势:")
print("  ✅ 避免命名冲突（前缀）")
print("  ✅ 任何地方直接访问")
print("  ✅ 可以创建简洁别名")
print("  ✅ 变量来源清晰")
print("  ✅ 性能最好")

print("\n⚠️ 备选：纯全局变量（小项目）")
print("代码示例:")
print("  A = 10")
print("  B = 20")
print("")
print("适用场景:")
print("  • 小型项目")
print("  • 变量名不会冲突")
print("  • 追求最大简洁性")

print("\n❌ 不推荐：继续使用模块表")
print("原因:")
print("  • 代码冗长（需要前缀）")
print("  • 跨模块访问复杂")
print("  • 性能较差")

print("\n\n8. 最终建议")
print("=" * 50)

print("\n对于您的需求，推荐使用带前缀的全局变量:")

print("\n在主脚本中:")
print("  MAIN_A = 10")
print("  MAIN_B = 20")
print("  MAIN_C = 30")
print("  -- 创建别名")
print("  A = MAIN_A")
print("  B = MAIN_B")
print("  C = MAIN_C")

print("\n在其他模块中:")
print("  -- 直接使用")
print("  local sum = A + B + C")
print("  -- 或明确使用")
print("  local sum = MAIN_A + MAIN_B + MAIN_C")

print("\n这样既避免了冲突，又保持了代码的简洁性！")

print("\n演示完成！")
