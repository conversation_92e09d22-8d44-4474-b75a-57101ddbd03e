# Lua全局变量使用指南

## 您的问题

> 主脚本中的 A、B、C 应该是全局变量？是否可以在任意地方，比如main和other中调用都不使用 main.xxx 这个前缀？

## 答案

✅ **完全可以！** 您可以将 A、B、C 设置为全局变量，这样在任何地方都可以直接使用，无需 `main.` 前缀。

## 三种实现方案

### 方案1：纯全局变量（最简单）

```lua
-- main脚本
A = 10
B = 20
C = 30

-- 任何地方都可以直接使用
print(A)  -- 直接访问
A = 100   -- 直接修改
```

**优点**：
- ✅ 最简洁，无需任何前缀
- ✅ 任何地方都能直接访问
- ✅ 代码最清晰
- ✅ 性能最好

**缺点**：
- ⚠️ 可能命名冲突
- ⚠️ 变量来源不明确

### 方案2：带前缀的全局变量（推荐）

```lua
-- main脚本
MAIN_A = 10
MAIN_B = 20
MAIN_C = 30

-- 创建简洁别名
A = MAIN_A
B = MAIN_B
C = MAIN_C

-- 使用方式
print(A)      -- 简洁访问
print(MAIN_A) -- 明确访问
```

**优点**：
- ✅ 避免命名冲突
- ✅ 变量来源清晰
- ✅ 支持简洁访问
- ✅ 任何地方都能访问

**缺点**：
- ⚠️ 需要管理别名同步

### 方案3：混合方案

```lua
-- 保留模块表（可选）
local main = {A = 10, B = 20, C = 30}

-- 同时设置全局变量
A = main.A
B = main.B
C = main.C
```

## 使用效果对比

### 原来的方式（模块表）

```lua
-- 设置
local main = {}
main.A = 10
main.B = 20
main.C = 30

-- 使用（需要前缀）
local sum = main.A + main.B + main.C
if main.A > main.B then
    main.C = main.C + (main.A - main.B)
end
```

### 全局变量方式

```lua
-- 设置
A = 10
B = 20
C = 30

-- 使用（无需前缀）
local sum = A + B + C
if A > B then
    C = C + (A - B)
end
```

## 跨模块访问对比

### 模块表方式

```lua
-- main.lua
local main = {A = 10, B = 20, C = 30}
local other = dofile("other.lua")

-- other.lua 中需要传递参数
function use_main_vars(main_module)
    return main_module.A + main_module.B
end

-- 调用时需要传递
local result = other.use_main_vars(main)
```

### 全局变量方式

```lua
-- main.lua
A = 10
B = 20
C = 30
dofile("other.lua")

-- other.lua 中直接使用
function use_main_vars()
    return A + B  -- 直接使用，无需传递
end

-- 调用时无需参数
local result = use_main_vars()
```

## 实际代码示例

### 在main中使用

```lua
-- 设置全局变量
A = 10
B = 20
C = 30

-- 直接使用
print("A = " .. A)
print("B = " .. B)
print("C = " .. C)

-- 直接修改
A = A + 100
B = B * 2

-- 直接计算
local result = A + B + C
```

### 在other模块中使用

```lua
-- other.lua
function other_calculate()
    -- 直接使用全局变量，无需前缀
    local sum = A + B + C
    local product = A * B * C
    
    -- 直接修改
    A = A + 10
    B = B + 20
    
    return sum, product
end
```

### 在third模块中使用

```lua
-- third.lua
function third_process()
    -- 直接在条件中使用
    if A > B then
        C = C + (A - B)
    end
    
    -- 直接在循环中使用
    for i = 1, 3 do
        A = A + i
        B = B + i * 2
    end
    
    return A + B + C
end
```

## 命名冲突解决

### 问题示例

```lua
-- main.lua
A = 10

-- other.lua
A = 999  -- 覆盖了main的A！
```

### 解决方案1：使用前缀

```lua
-- main.lua
MAIN_A = 10
MAIN_B = 20
MAIN_C = 30

-- other.lua
OTHER_A = 100
OTHER_B = 200
OTHER_C = 300

-- 创建别名（可选）
A = MAIN_A  -- 指向主脚本的A
```

### 解决方案2：命名约定

```lua
-- 使用有意义的变量名
VELOCITY = 10    -- 代替 A
TIME = 20        -- 代替 B
ACCELERATION = 30 -- 代替 C
```

## 性能对比

| 访问方式 | 代码示例 | 性能 |
|----------|----------|------|
| 全局变量 | `A + B + C` | 最快 |
| 模块表 | `main.A + main.B + main.C` | 较慢 |

全局变量访问比模块表访问快约 **2-3倍**。

## 推荐方案

### 🏆 推荐：带前缀的全局变量

```lua
-- main.lua
MAIN_A = 10
MAIN_B = 20
MAIN_C = 30

-- 创建别名（可选）
A = MAIN_A
B = MAIN_B
C = MAIN_C

-- 同步函数
function sync_main_vars()
    A = MAIN_A
    B = MAIN_B
    C = MAIN_C
end
```

**选择理由**：
1. ✅ 避免命名冲突
2. ✅ 任何地方都能直接访问
3. ✅ 可以创建简洁别名
4. ✅ 变量来源清晰
5. ✅ 性能优秀

### 使用建议

1. **小项目**：可以直接使用 `A = 10`
2. **中大项目**：推荐使用 `MAIN_A = 10` + 别名
3. **团队项目**：建立命名约定，使用有意义的变量名

## 总结

您的想法是完全正确的！将主脚本的 A、B、C 设置为全局变量，可以：

- ✅ 在任何地方直接使用 `A`、`B`、`C`
- ✅ 无需 `main.A` 前缀
- ✅ 代码更简洁
- ✅ 性能更好
- ✅ 跨模块访问更方便

**推荐使用带前缀的全局变量方案**，既保持了简洁性，又避免了命名冲突！
