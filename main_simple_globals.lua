-- 主脚本 - 简单全局变量版本
-- 主脚本用简单的 A, B, C
-- 其他模块用 other.xxx, third.xxx 避免冲突

print("=== 主脚本简单全局变量版本 ===\n")

-- 主脚本直接使用简单的全局变量
A = 10
B = 20
C = 30
main_name = "main_simple_globals"

print("主脚本设置了简单的全局变量:")
print("A = " .. A)
print("B = " .. B)
print("C = " .. C)

-- 加载其他模块（它们使用模块化命名）
print("\n加载其他模块...")
local other = dofile("other_modular_naming.lua")
local third = dofile("third_modular_naming.lua")

-- 在main中直接使用简单变量名
print("\n在main中直接使用:")

-- 最简洁的使用方式
local sum = A + B + C
local product = A * B * C
print("A + B + C = " .. sum)
print("A * B * C = " .. product)

-- 直接修改
print("\n在main中修改:")
A = A + 50
B = B * 1.5
C = C + 25
print("修改后: A=" .. A .. ", B=" .. B .. ", C=" .. C)

-- 简洁的条件逻辑
print("\n条件逻辑:")
if A > B then
    C = C + 10
    print("因为 A > B，所以 C 增加到 " .. C)
end

-- 简洁的循环
print("\n循环操作:")
for i = 1, 3 do
    A = A + i
    print("第" .. i .. "次循环: A = " .. A)
end

-- 函数中直接使用
function main_calculate()
    print("\nmain函数中直接使用全局变量:")
    
    -- 最简洁的代码
    local result = A * 2 + B * 3 + C * 4
    print("A*2 + B*3 + C*4 = " .. result)
    
    -- 直接修改
    A = A + 1
    B = B + 2
    C = C + 3
    
    return result
end

local calc_result = main_calculate()

-- 与其他模块交互
print("\n与其他模块交互:")

-- 访问other模块的变量（使用模块化命名）
print("访问other模块:")
print("other.X = " .. other.X)
print("other.Y = " .. other.Y)
print("other.Z = " .. other.Z)

-- 访问third模块的变量
print("访问third模块:")
print("third.X = " .. third.X)
print("third.Y = " .. third.Y)
print("third.Z = " .. third.Z)

-- 跨模块计算（混合使用）
print("\n跨模块计算:")
local cross_calc1 = A + other.X  -- 主脚本的A + other模块的X
local cross_calc2 = B + third.Y  -- 主脚本的B + third模块的Y
local cross_calc3 = C + other.Z + third.Z  -- 混合计算

print("A + other.X = " .. cross_calc1)
print("B + third.Y = " .. cross_calc2)
print("C + other.Z + third.Z = " .. cross_calc3)

-- 调用其他模块的函数
print("\n调用其他模块函数:")
if other.use_main_vars then
    other.use_main_vars()  -- other模块会直接使用 A, B, C
end

if third.use_main_vars then
    third.use_main_vars()  -- third模块会直接使用 A, B, C
end

-- 复杂的联合操作
print("\n复杂联合操作:")
function complex_joint_operation()
    print("执行复杂联合操作:")
    
    -- 主脚本变量：直接使用
    local main_sum = A + B + C
    
    -- other模块变量：使用模块前缀
    local other_sum = other.X + other.Y + other.Z
    
    -- third模块变量：使用模块前缀
    local third_sum = third.X + third.Y + third.Z
    
    print("主脚本总和: " .. main_sum)
    print("other模块总和: " .. other_sum)
    print("third模块总和: " .. third_sum)
    
    -- 联合计算
    local total = main_sum + other_sum + third_sum
    print("总计: " .. total)
    
    -- 根据结果调整主脚本变量（最简洁）
    if total > 10000 then
        A = A * 0.9
        B = B * 0.9
        C = C * 0.9
        print("总和过大，调整主脚本变量")
    end
    
    return total
end

local joint_result = complex_joint_operation()

-- 批量操作演示
print("\n批量操作演示:")
function batch_operations()
    print("批量修改所有模块变量:")
    
    -- 主脚本变量：直接操作（最简洁）
    A = A + 10
    B = B + 20
    C = C + 30
    
    -- other模块变量：通过模块表操作
    other.X = other.X + 100
    other.Y = other.Y + 200
    other.Z = other.Z + 300
    
    -- third模块变量：通过模块表操作
    third.X = third.X + 1000
    third.Y = third.Y + 2000
    third.Z = third.Z + 3000
    
    print("批量操作完成")
end

batch_operations()

-- 最终状态展示
print("\n=== 最终状态 ===")
function print_all_variables()
    print("主脚本变量（直接全局）:")
    print("  A = " .. A)
    print("  B = " .. B)
    print("  C = " .. C)
    
    print("other模块变量（模块化命名）:")
    print("  other.X = " .. other.X)
    print("  other.Y = " .. other.Y)
    print("  other.Z = " .. other.Z)
    
    print("third模块变量（模块化命名）:")
    print("  third.X = " .. third.X)
    print("  third.Y = " .. third.Y)
    print("  third.Z = " .. third.Z)
end

print_all_variables()

-- 验证无命名冲突
print("\n=== 验证无命名冲突 ===")
print("✅ 主脚本使用: A, B, C")
print("✅ other模块使用: other.X, other.Y, other.Z")
print("✅ third模块使用: third.X, third.Y, third.Z")
print("✅ 完全没有命名冲突！")

print("\n=== 方案优势 ===")
print("✅ 主脚本代码最简洁（直接用 A, B, C）")
print("✅ 其他模块有清晰的命名空间")
print("✅ 完全避免命名冲突")
print("✅ 跨模块访问清晰明确")
print("✅ 性能优秀（主脚本直接全局变量访问）")

print("\n=== 使用体验 ===")
print("• 在main中: 直接用 A, B, C（最简洁）")
print("• 访问other: 用 other.X, other.Y, other.Z")
print("• 访问third: 用 third.X, third.Y, third.Z")
print("• 跨模块计算: A + other.X + third.Y")

print("\n您的方案是最佳的！既简洁又安全！")
