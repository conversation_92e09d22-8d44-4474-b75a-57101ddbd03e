-- other模块 - 局部变量版本
-- 使用 local A = 100 的方式

print("=== other_local_vars.lua 开始加载 ===")

-- 方式2：局部变量 local A = 100
local A = 100
local B = 200
local C = 300
local name = "other_local_vars"
local version = "3.0"

print("设置了局部变量 A, B, C")

-- 局部函数
local function print_variables()
    print("=== other模块变量（局部版本）===")
    print("local A = " .. A)
    print("local B = " .. B)
    print("local C = " .. C)
    print("local name = " .. name)
end

local function calculate()
    local sum = A + B + C
    local product = A * B * C
    
    print("other模块计算结果:")
    print("sum = " .. sum)
    print("product = " .. product)
    
    return {sum = sum, product = product}
end

local function use_third_variables()
    print("=== other模块访问third模块 ===")
    
    if _G.third then
        print("访问 third.X = " .. third.X)
        print("访问 third.Y = " .. third.Y)
        
        -- 修改third模块的变量
        third.X = third.X + 25
        print("修改third.X为: " .. third.X)
    else
        print("third模块未找到")
    end
end

local function collaborate_with_third()
    print("=== other与third协作（局部变量版本）===")
    
    if not _G.third then
        print("third模块未找到")
        return 0
    end
    
    -- 注意：这里只能读取局部变量，外部无法修改
    print("局部变量 A = " .. A)
    print("third.X = " .. third.X)
    
    -- 可以修改third的变量，但外部无法修改我们的局部变量
    third.X = third.X + A
    
    return A + B + C
end

-- 获取局部变量的函数
local function get_A()
    return A
end

local function set_A(value)
    A = value
    print("设置局部变量 A = " .. A)
end

print("other_local_vars.lua 加载完成")

-- 返回一个表，包含访问局部变量的接口
return {
    -- 变量访问器
    get_A = get_A,
    set_A = set_A,
    get_B = function() return B end,
    set_B = function(value) B = value end,
    get_C = function() return C end,
    set_C = function(value) C = value end,
    
    -- 函数
    print_variables = print_variables,
    calculate = calculate,
    use_third_variables = use_third_variables,
    collaborate_with_third = collaborate_with_third,
    
    -- 属性
    name = name,
    version = version
}
