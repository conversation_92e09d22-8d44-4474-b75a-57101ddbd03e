-- other模块 - 模块化命名版本
-- 使用 other.xxx 命名，避免与主脚本的 A, B, C 冲突

print("=== other_modular_naming.lua 开始加载 ===")

-- 创建模块表
local other = {}

-- 模块变量使用模块化命名
other.X = 1000
other.Y = 2000
other.Z = 3000
other.name = "other_modular_naming"
other.version = "1.0"

print("other模块使用模块化命名: other.X, other.Y, other.Z")

-- 模块函数
function other.print_variables()
    print("=== other模块变量 ===")
    print("other.X = " .. other.X)
    print("other.Y = " .. other.Y)
    print("other.Z = " .. other.Z)
end

function other.calculate()
    local sum = other.X + other.Y + other.Z
    local product = other.X * other.Y * other.Z
    local average = sum / 3
    
    print("other模块计算:")
    print("  sum = " .. sum)
    print("  product = " .. product)
    print("  average = " .. average)
    
    return {sum = sum, product = product, average = average}
end

-- 直接使用主脚本的全局变量 A, B, C
function other.use_main_vars()
    print("=== other模块使用主脚本的全局变量 ===")
    
    -- 直接使用 A, B, C（来自主脚本）
    print("直接访问主脚本的 A = " .. A)
    print("直接访问主脚本的 B = " .. B)
    print("直接访问主脚本的 C = " .. C)
    
    -- 与自己的变量混合计算
    local mixed_calc1 = A + other.X  -- 主脚本的A + other模块的X
    local mixed_calc2 = B * other.Y  -- 主脚本的B * other模块的Y
    local mixed_calc3 = C + other.Z  -- 主脚本的C + other模块的Z
    
    print("混合计算:")
    print("  A + other.X = " .. mixed_calc1)
    print("  B * other.Y = " .. mixed_calc2)
    print("  C + other.Z = " .. mixed_calc3)
    
    -- 修改主脚本的变量
    print("other模块修改主脚本变量:")
    A = A + 5
    B = B + 10
    C = C + 15
    print("  修改后 A = " .. A)
    print("  修改后 B = " .. B)
    print("  修改后 C = " .. C)
end

-- 复杂的混合操作
function other.complex_mixed_operations()
    print("=== other模块复杂混合操作 ===")
    
    -- 同时使用主脚本变量和模块变量
    print("当前状态:")
    print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    print("  other模块: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
    
    -- 复杂的条件逻辑
    if A > other.X / 100 then
        print("A 相对较大，调整策略1")
        other.X = other.X * 1.1
        A = A * 0.9
    elseif A < other.X / 200 then
        print("A 相对较小，调整策略2")
        other.X = other.X * 0.9
        A = A * 1.1
    end
    
    -- 比例调整
    local ratio = (A + B + C) / (other.X + other.Y + other.Z)
    print("主脚本与other模块的比例: " .. ratio)
    
    if ratio > 0.1 then
        print("比例过高，调整other模块变量")
        other.Y = other.Y * 1.2
        other.Z = other.Z * 1.2
    elseif ratio < 0.05 then
        print("比例过低，调整主脚本变量")
        B = B * 1.2
        C = C * 1.2
    end
    
    print("调整后:")
    print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    print("  other模块: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
end

-- 与third模块协作
function other.collaborate_with_third()
    print("=== other与third模块协作 ===")
    
    if _G.third then
        print("协作前状态:")
        print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
        print("  third: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
        
        -- 三方数据交换
        local temp = A
        A = other.X / 100
        other.X = third.X / 10
        third.X = temp * 1000
        
        print("三方数据交换完成")
        print("协作后状态:")
        print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
        print("  third: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
        
        return A + other.X + third.X
    else
        print("third模块未找到")
        return A + other.X
    end
end

-- 批量操作
function other.batch_operations(operations)
    print("=== other模块批量操作 ===")
    
    for i, op in ipairs(operations) do
        if op.target == "main" then
            -- 操作主脚本变量
            if op.var == "A" then
                if op.type == "add" then A = A + op.value
                elseif op.type == "multiply" then A = A * op.value
                elseif op.type == "set" then A = op.value
                end
            elseif op.var == "B" then
                if op.type == "add" then B = B + op.value
                elseif op.type == "multiply" then B = B * op.value
                elseif op.type == "set" then B = op.value
                end
            elseif op.var == "C" then
                if op.type == "add" then C = C + op.value
                elseif op.type == "multiply" then C = C * op.value
                elseif op.type == "set" then C = op.value
                end
            end
            print("操作" .. i .. ": " .. op.type .. " 主脚本." .. op.var .. " " .. op.value)
            
        elseif op.target == "other" then
            -- 操作other模块变量
            if op.var == "X" then
                if op.type == "add" then other.X = other.X + op.value
                elseif op.type == "multiply" then other.X = other.X * op.value
                elseif op.type == "set" then other.X = op.value
                end
            elseif op.var == "Y" then
                if op.type == "add" then other.Y = other.Y + op.value
                elseif op.type == "multiply" then other.Y = other.Y * op.value
                elseif op.type == "set" then other.Y = op.value
                end
            elseif op.var == "Z" then
                if op.type == "add" then other.Z = other.Z + op.value
                elseif op.type == "multiply" then other.Z = other.Z * op.value
                elseif op.type == "set" then other.Z = op.value
                end
            end
            print("操作" .. i .. ": " .. op.type .. " other." .. op.var .. " " .. op.value)
        end
    end
    
    print("批量操作完成")
end

-- 演示命名空间的清晰性
function other.demonstrate_namespace()
    print("=== 演示命名空间清晰性 ===")
    
    print("变量来源一目了然:")
    print("  A, B, C          <- 来自主脚本（全局变量）")
    print("  other.X, Y, Z    <- 来自other模块")
    print("  third.X, Y, Z    <- 来自third模块（如果存在）")
    
    print("\n代码示例:")
    print("  local result = A + other.X + third.Y")
    print("  含义: 主脚本的A + other模块的X + third模块的Y")
    
    -- 实际计算演示
    if _G.third then
        local demo_calc = A + other.X + third.Y
        print("\n实际计算: A + other.X + third.Y = " .. demo_calc)
    else
        local demo_calc = A + other.X
        print("\n实际计算: A + other.X = " .. demo_calc)
    end
end

-- 获取模块状态
function other.get_status()
    return {
        module_vars = {X = other.X, Y = other.Y, Z = other.Z},
        main_vars = {A = A, B = B, C = C},
        module_name = other.name
    }
end

print("other_modular_naming.lua 加载完成")

return other
