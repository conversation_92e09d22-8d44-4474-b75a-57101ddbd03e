-- 第三个Lua脚本文件 - 模块化版本
-- 创建模块表
local third = {}

-- 模块内部的变量
third.X = 1000
third.Y = 2000
third.Z = 3000
third.name = "third_script"
third.version = "1.0"
third.description = "第三个模块"

-- 模块函数：显示当前模块的变量值
function third.print_variables()
    print("=== third模块中的变量 ===")
    print("third.X = " .. third.X)
    print("third.Y = " .. third.Y)
    print("third.Z = " .. third.Z)
    print("third.name = " .. third.name)
    print("third.version = " .. third.version)
    print("third.description = " .. third.description)
end

-- 模块计算函数
function third.calculate()
    local sum = third.X + third.Y + third.Z
    local product = third.X * third.Y * third.Z
    local average = sum / 3
    
    print("third.X + third.Y + third.Z = " .. sum)
    print("third.X * third.Y * third.Z = " .. product)
    print("平均值 = " .. average)
    
    return {sum = sum, product = product, average = average}
end

-- 与other模块交互的函数
function third.interact_with_other(other_module)
    if not other_module then
        print("错误：未提供other模块")
        return
    end
    
    print("=== third模块与other模块交互 ===")
    
    -- 读取other模块的变量
    local other_A = other_module.A
    local other_B = other_module.B
    local other_C = other_module.C
    
    print("从other模块读取: A=" .. other_A .. ", B=" .. other_B .. ", C=" .. other_C)
    
    -- 使用other模块的变量进行计算
    local combined_sum = third.X + other_A
    print("third.X + other.A = " .. combined_sum)
    
    -- 修改other模块的变量
    other_module.A = other_A + 10
    other_module.B = other_B + 20
    print("修改other模块变量: A=" .. other_module.A .. ", B=" .. other_module.B)
    
    -- 调用other模块的函数
    print("调用other模块的计算函数:")
    other_module.calculate()
    
    return combined_sum
end

-- 批量操作函数
function third.batch_operation(other_module)
    if not other_module then
        return
    end
    
    print("=== 批量操作演示 ===")
    
    -- 保存原始值
    local original_values = {
        other_A = other_module.A,
        other_B = other_module.B,
        other_C = other_module.C
    }
    
    -- 批量修改other模块的值
    other_module.A = third.X / 10
    other_module.B = third.Y / 10
    other_module.C = third.Z / 10
    
    print("批量修改other模块变量完成")
    other_module.print_variables()
    
    -- 恢复原始值
    other_module.A = original_values.other_A
    other_module.B = original_values.other_B
    other_module.C = original_values.other_C
    
    print("恢复other模块原始值")
    other_module.print_variables()
end

-- 获取变量函数
function third.get_variables()
    return {X = third.X, Y = third.Y, Z = third.Z}
end

-- 修改变量函数
function third.set_variables(new_X, new_Y, new_Z)
    if new_X then third.X = new_X end
    if new_Y then third.Y = new_Y end
    if new_Z then third.Z = new_Z end
    
    print("third模块变量已更新:")
    third.print_variables()
end

-- 获取所有变量和信息
function third.get_all()
    return {
        X = third.X,
        Y = third.Y,
        Z = third.Z,
        name = third.name,
        version = third.version,
        description = third.description
    }
end

-- 与多个模块交互
function third.multi_module_interaction(modules)
    print("=== 多模块交互演示 ===")
    
    for name, module in pairs(modules) do
        print("与模块 " .. name .. " 交互:")
        if module.print_variables then
            module.print_variables()
        end
        if module.calculate then
            module.calculate()
        end
        print("")
    end
end

-- 脚本被加载时自动执行
print("third_script.lua 模块已被加载")
third.print_variables()

-- 返回模块表
return third
