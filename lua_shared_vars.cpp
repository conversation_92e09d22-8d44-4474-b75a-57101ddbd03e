#include <lua.hpp>
#include <iostream>
#include <unordered_map>
#include <string>

// 全局变量存储
static std::unordered_map<std::string, double> shared_vars;

// __index 元方法：当访问不存在的变量时调用
static int shared_index(lua_State* L) {
    // 获取键名
    const char* key = lua_tostring(L, 2);
    
    if (key && shared_vars.find(key) != shared_vars.end()) {
        // 如果在共享变量中找到，返回该值
        lua_pushnumber(L, shared_vars[key]);
        std::cout << "从共享存储读取变量 " << key << " = " << shared_vars[key] << std::endl;
        return 1;
    }
    
    // 如果没找到，返回nil
    lua_pushnil(L);
    return 1;
}

// __newindex 元方法：当设置新变量时调用
static int shared_newindex(lua_State* L) {
    // 获取键名和值
    const char* key = lua_tostring(L, 2);
    double value = lua_tonumber(L, 3);
    
    if (key) {
        // 存储到共享变量中
        shared_vars[key] = value;
        std::cout << "设置共享变量 " << key << " = " << value << std::endl;
        
        // 同时设置到全局环境中
        lua_getglobal(L, "_G");
        lua_pushstring(L, key);
        lua_pushnumber(L, value);
        lua_settable(L, -3);
        lua_pop(L, 1);
    }
    
    return 0;
}

// 初始化共享变量系统
static int init_shared_vars(lua_State* L) {
    // 创建元表
    luaL_newmetatable(L, "SharedVarsMetatable");
    
    // 设置 __index 元方法
    lua_pushstring(L, "__index");
    lua_pushcfunction(L, shared_index);
    lua_settable(L, -3);
    
    // 设置 __newindex 元方法
    lua_pushstring(L, "__newindex");
    lua_pushcfunction(L, shared_newindex);
    lua_settable(L, -3);
    
    // 获取全局环境表
    lua_getglobal(L, "_G");
    
    // 设置元表到全局环境
    lua_pushvalue(L, -2);  // 复制元表
    lua_setmetatable(L, -2);
    
    lua_pop(L, 2);  // 清理栈
    
    std::cout << "共享变量系统已初始化" << std::endl;
    return 0;
}

// 设置共享变量
static int set_shared_var(lua_State* L) {
    const char* key = lua_tostring(L, 1);
    double value = lua_tonumber(L, 2);
    
    if (key) {
        shared_vars[key] = value;
        
        // 同时设置到全局环境
        lua_getglobal(L, "_G");
        lua_pushstring(L, key);
        lua_pushnumber(L, value);
        lua_settable(L, -3);
        lua_pop(L, 1);
        
        std::cout << "设置共享变量 " << key << " = " << value << std::endl;
    }
    
    return 0;
}

// 获取共享变量
static int get_shared_var(lua_State* L) {
    const char* key = lua_tostring(L, 1);
    
    if (key && shared_vars.find(key) != shared_vars.end()) {
        lua_pushnumber(L, shared_vars[key]);
        return 1;
    }
    
    lua_pushnil(L);
    return 1;
}

// 打印所有共享变量
static int print_shared_vars(lua_State* L) {
    std::cout << "=== 所有共享变量 ===" << std::endl;
    for (const auto& pair : shared_vars) {
        std::cout << pair.first << " = " << pair.second << std::endl;
    }
    return 0;
}

// 注册函数到Lua
static const luaL_Reg shared_vars_lib[] = {
    {"init", init_shared_vars},
    {"set", set_shared_var},
    {"get", get_shared_var},
    {"print_all", print_shared_vars},
    {NULL, NULL}
};

// 模块入口点
extern "C" int luaopen_shared_vars(lua_State* L) {
    luaL_newlib(L, shared_vars_lib);
    return 1;
}
