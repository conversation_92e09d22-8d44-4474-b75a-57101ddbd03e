import  os
from datetime import datetime
#写日志文件
def __writeToLog(tempstr,varfileName=None):
    try:
        slog_path = os.getcwd()
        slog_path = slog_path + '\pyLog'
        slog_path = r'D:\pyLog'
        if not os.path.exists(slog_path):
            os.makedirs(slog_path, exist_ok=True)

        now = datetime.now()
        # 格式化时间为字符串
        if varfileName == None:
            formatted_time = now.strftime('%Y-%m-%d_') + '.log'
        else:
            formatted_time = now.strftime('%Y-%m-%d_')+str(varfileName) + '.log'

        slog_path = slog_path + '\\' + formatted_time

        # 打开文件，模式为 'w' 表示写模式，如果文件已存在则会被覆盖
        with open(slog_path, 'a', encoding='utf-8') as file:
            # 写入文本到文件
            now = datetime.now()
            # 格式化时间为字符串
            sformatted_time = now.strftime('%Y-%m-%d %H:%M:%S  ')
            file.write(sformatted_time + tempstr + '\n')
    except Exception as e:
        errorStr = str(e)
        print('errorStr',errorStr)

def __Device_ErrorWrite(seq_context,WriteMode,varvalue,varfileName=None):
    try:
        ivarvalue=int(varvalue)
        if seq_context is not None:
            if WriteMode==0:
                seq_context.SetValNumber("StationGlobals.Devices."+varfileName+".DeviceState", 1, ivarvalue)
            elif WriteMode==1:
                seq_context.SetValNumber("StationGlobals.Devices."+varfileName+".DeviceState", 1, ivarvalue)
                #seq_context.SetValNumber("StationGlobals.Devices.高压直流源载.WriteState", 1, ivarvalue)
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__Device_ErrorWrite error ' + errorStr)
        return (conFuncErr, errorStr, True)


