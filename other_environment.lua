-- other模块 - 环境变量版本
-- 最简洁的内外部访问分离方案

print("=== other_environment.lua 开始加载 ===")

-- 创建模块表
local other = {}

-- 创建模块的私有环境
local module_env = {
    -- 模块变量
    A = 100,
    B = 200,
    C = 300,
    name = "other_environment",
    version = "4.2",
    
    -- 继承全局环境
    print = print,
    tostring = tostring,
    pairs = pairs,
    ipairs = ipairs,
    type = type,
    math = math,
    string = string,
    table = table,
    
    -- 访问全局模块
    _G = _G
}

-- 设置环境的元表，允许访问全局变量
setmetatable(module_env, {
    __index = _G
})

-- 同步函数：将环境变量同步到模块表
local function sync_to_module()
    other.A = module_env.A
    other.B = module_env.B
    other.C = module_env.C
    other.name = module_env.name
    other.version = module_env.version
end

-- 初始同步
sync_to_module()

print("设置了模块私有环境")

-- 在模块环境中定义内部函数
local function create_internal_functions()
    
    -- 内部计算函数
    local function internal_calculate()
        -- 直接使用 A, B, C
        local sum = A + B + C
        local product = A * B * C
        local average = sum / 3
        
        print("内部计算（环境变量）:")
        print("  A + B + C = " .. sum)
        print("  A * B * C = " .. product)
        print("  平均值 = " .. average)
        
        return {sum = sum, product = product, average = average}
    end
    
    -- 内部修改函数
    local function internal_modify(delta_A, delta_B, delta_C)
        print("内部修改变量:")
        
        if delta_A then
            A = A + delta_A
            print("  A = " .. A)
        end
        
        if delta_B then
            B = B + delta_B
            print("  B = " .. B)
        end
        
        if delta_C then
            C = C + delta_C
            print("  C = " .. C)
        end
        
        -- 同步到外部
        sync_to_module()
    end
    
    -- 内部打印函数
    local function internal_print()
        print("=== 内部变量状态 ===")
        print("A = " .. A)
        print("B = " .. B)
        print("C = " .. C)
        print("name = " .. name)
    end
    
    -- 内部复杂逻辑
    local function internal_complex()
        print("内部复杂逻辑:")
        
        -- 直接使用变量，代码简洁
        local total = A + B + C
        
        if total > 500 then
            -- 按比例缩放
            local scale = 500 / total
            A = A * scale
            B = B * scale
            C = C * scale
            print("  总和超过500，按比例缩放")
        elseif total < 200 then
            -- 按比例放大
            local scale = 200 / total
            A = A * scale
            B = B * scale
            C = C * scale
            print("  总和小于200，按比例放大")
        end
        
        print("  调整后 A = " .. A)
        print("  调整后 B = " .. B)
        print("  调整后 C = " .. C)
        
        sync_to_module()
        return A + B + C
    end
    
    -- 内部访问其他模块
    local function internal_use_third()
        print("内部访问third模块:")
        
        -- 通过_G访问全局模块
        if _G.third then
            local third = _G.third
            
            print("  third.X = " .. third.X)
            print("  third.Y = " .. third.Y)
            
            -- 使用内部变量和third模块进行计算
            local ratio = A / third.X
            print("  A / third.X = " .. ratio)
            
            -- 根据比例调整
            if ratio > 0.5 then
                A = A * 0.9
                print("  比例过高，调整A = " .. A)
            elseif ratio < 0.1 then
                A = A * 1.1
                print("  比例过低，调整A = " .. A)
            end
            
            -- 影响third模块
            third.Y = third.Y + B
            print("  设置third.Y = " .. third.Y)
            
            sync_to_module()
            return ratio
        else
            print("  third模块未找到")
            return 0
        end
    end
    
    -- 批量操作
    local function internal_batch_ops(operations)
        print("内部批量操作:")
        
        for i, op in ipairs(operations) do
            if op.type == "set" then
                if op.var == "A" then A = op.value
                elseif op.var == "B" then B = op.value
                elseif op.var == "C" then C = op.value
                end
                print("  设置 " .. op.var .. " = " .. op.value)
                
            elseif op.type == "add" then
                if op.var == "A" then A = A + op.value
                elseif op.var == "B" then B = B + op.value
                elseif op.var == "C" then C = C + op.value
                end
                print("  增加 " .. op.var .. " += " .. op.value)
                
            elseif op.type == "multiply" then
                if op.var == "A" then A = A * op.value
                elseif op.var == "B" then B = B * op.value
                elseif op.var == "C" then C = C * op.value
                end
                print("  乘以 " .. op.var .. " *= " .. op.value)
            end
        end
        
        sync_to_module()
        print("批量操作完成")
    end
    
    -- 设置所有函数的环境
    setfenv(internal_calculate, module_env)
    setfenv(internal_modify, module_env)
    setfenv(internal_print, module_env)
    setfenv(internal_complex, module_env)
    setfenv(internal_use_third, module_env)
    setfenv(internal_batch_ops, module_env)
    
    return {
        calculate = internal_calculate,
        modify = internal_modify,
        print_vars = internal_print,
        complex = internal_complex,
        use_third = internal_use_third,
        batch_ops = internal_batch_ops
    }
end

-- 创建内部函数
local internal = create_internal_functions()

-- 外部接口函数
function other.print_variables()
    print("=== other模块变量（环境版本）===")
    print("other.A = " .. other.A)
    print("other.B = " .. other.B)
    print("other.C = " .. other.C)
    print("other.name = " .. other.name)
    
    print("\n内部环境状态:")
    internal.print_vars()
end

function other.calculate()
    print("外部调用计算")
    return internal.calculate()
end

function other.modify_variables(delta_A, delta_B, delta_C)
    print("外部调用修改")
    internal.modify(delta_A, delta_B, delta_C)
end

function other.run_complex_logic()
    print("外部调用复杂逻辑")
    return internal.complex()
end

function other.use_third_variables()
    print("外部调用访问third")
    return internal.use_third()
end

function other.batch_operations(operations)
    print("外部调用批量操作")
    internal.batch_ops(operations)
end

-- 演示内外部访问
function other.demo_environment()
    print("\n=== 演示环境变量方案 ===")
    
    print("1. 外部访问:")
    print("   other.A = " .. other.A)
    print("   other.B = " .. other.B)
    
    print("\n2. 内部计算（直接使用变量名）:")
    local result = internal.calculate()
    
    print("\n3. 内部修改:")
    internal.modify(10, 20, 30)
    
    print("\n4. 验证同步:")
    print("   修改后 other.A = " .. other.A)
    print("   修改后 other.B = " .. other.B)
    print("   修改后 other.C = " .. other.C)
    
    return result
end

-- 获取环境状态
function other.get_environment_state()
    return {
        external = {A = other.A, B = other.B, C = other.C},
        internal = {A = module_env.A, B = module_env.B, C = module_env.C},
        synced = (other.A == module_env.A and other.B == module_env.B and other.C == module_env.C)
    }
end

-- 手动同步
function other.sync()
    sync_to_module()
    print("手动同步完成")
end

print("other_environment.lua 加载完成")

return other
