-- 变量作用域对比演示

print("=== Lua模块变量作用域对比演示 ===\n")

-- 清理环境
_G.A = nil
_G.B = nil
_G.C = nil
_G.other = nil
_G.third = nil

print("1. 测试直接全局变量方案:")
print("加载 other_direct_global.lua...")

-- 加载直接全局变量版本
dofile("other_direct_global.lua")

print("\n检查全局变量:")
print("全局 A = " .. tostring(_G.A))
print("全局 B = " .. tostring(_G.B))
print("全局 C = " .. tostring(_G.C))

print("\n在main中访问:")
print("A = " .. A)  -- 直接访问全局变量
print("B = " .. B)
A = 555  -- 直接修改
print("修改后 A = " .. A)

print("\n调用全局函数:")
print_variables()  -- 直接调用全局函数

-- 清理
_G.A = nil
_G.B = nil
_G.C = nil
_G.print_variables = nil
_G.calculate = nil

print("\n" .. "="*50)
print("2. 测试局部变量方案:")
print("加载 other_local_vars.lua...")

-- 加载局部变量版本
local other_local = dofile("other_local_vars.lua")

print("\n检查全局变量:")
print("全局 A = " .. tostring(_G.A))  -- 应该是 nil

print("\n通过模块接口访问:")
print("other_local.get_A() = " .. other_local.get_A())
other_local.set_A(777)
print("设置后 other_local.get_A() = " .. other_local.get_A())

print("\n调用模块函数:")
other_local.print_variables()

print("\n" .. "="*50)
print("3. 测试混合方案:")
print("加载 other_hybrid.lua...")

-- 加载混合版本
local other_hybrid = dofile("other_hybrid.lua")

print("\n检查访问方式:")
print("other_hybrid.A = " .. other_hybrid.A)
print("全局 other_A = " .. tostring(_G.other_A))

print("\n修改测试:")
other_hybrid.A = 888
other_hybrid.sync_global_aliases()
print("修改后 other_hybrid.A = " .. other_hybrid.A)
print("修改后 other_A = " .. _G.other_A)

print("\n演示多种访问方式:")
other_hybrid.demo_access_methods()

print("\n" .. "="*50)
print("4. 对比总结:")

print("\n=== 方案对比 ===")

print("\n方案1：直接全局变量 (A = 100)")
print("✅ 优点:")
print("  - 最简单，直接使用")
print("  - 可以写成 A = 100")
print("  - 在任何地方都能直接访问")
print("❌ 缺点:")
print("  - 污染全局命名空间")
print("  - 容易命名冲突")
print("  - 没有模块封装")

print("\n方案2：局部变量 (local A = 100)")
print("✅ 优点:")
print("  - 不污染全局命名空间")
print("  - 变量私有，安全性好")
print("❌ 缺点:")
print("  - 外部无法直接访问 other.A")
print("  - 需要通过函数接口访问")
print("  - 不支持 other.xxxvar 语法")

print("\n方案3：模块表 (other.A = 100)")
print("✅ 优点:")
print("  - 支持 other.xxxvar 语法")
print("  - 有命名空间，避免冲突")
print("  - 可以直接访问和修改")
print("  - 模块化设计")
print("❌ 缺点:")
print("  - 需要返回模块表")

print("\n=== 回答您的问题 ===")

print("\n问题1: 是否可以删除 local other = {}？")
print("答案: 可以，但会失去模块化的优势")

print("\n问题2: 变量可以写成 local A = 100？")
print("答案: 可以，但外部无法通过 other.A 访问")

print("\n问题3: 不能写 A = 100？")
print("答案: 可以写，但会创建全局变量，可能造成命名冲突")

print("\n=== 推荐方案 ===")
print("对于您的需求（支持 other.xxxvar 语法），推荐:")
print("1. 保留 local other = {}")
print("2. 使用 other.A = 100")
print("3. 返回 other 表")
print("这样既支持 other.xxxvar 语法，又保持了模块化设计")

print("\n=== 实际使用建议 ===")
print("如果您想要最简单的方案，可以:")
print("1. 删除 local other = {}")
print("2. 直接使用 A = 100 (全局变量)")
print("3. 不返回任何东西")
print("但这样会失去命名空间的保护")

print("\n演示完成！")
