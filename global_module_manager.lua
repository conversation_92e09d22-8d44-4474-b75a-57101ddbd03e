-- 全局模块管理器
-- 在main中统一导入，所有模块都可以访问

local GlobalModuleManager = {}

-- 全局模块存储
_G.MODULES = _G.MODULES or {}

-- 注册模块到全局环境
function GlobalModuleManager.register(name, module)
    _G.MODULES[name] = module
    
    -- 同时设置为全局变量，方便直接访问
    _G[name] = module
    
    print("模块 " .. name .. " 已注册到全局环境")
    
    -- 如果模块有初始化函数，调用它
    if module.init and type(module.init) == "function" then
        module.init()
    end
    
    return module
end

-- 获取模块
function GlobalModuleManager.get(name)
    return _G.MODULES[name]
end

-- 列出所有模块
function GlobalModuleManager.list()
    print("=== 已注册的全局模块 ===")
    for name, module in pairs(_G.MODULES) do
        local var_count = 0
        for k, v in pairs(module) do
            if type(v) ~= "function" and not k:match("^_") then
                var_count = var_count + 1
            end
        end
        print(name .. ": " .. var_count .. " 个变量")
    end
end

-- 创建模块间的直接访问接口
function GlobalModuleManager.enable_cross_access()
    for name, module in pairs(_G.MODULES) do
        -- 为每个模块添加访问其他模块的方法
        if not module._cross_access_enabled then
            module._cross_access_enabled = true
            
            -- 添加获取其他模块变量的方法
            module.get_from = function(module_name, var_name)
                if _G.MODULES[module_name] then
                    local value = _G.MODULES[module_name][var_name]
                    print(name .. " 读取 " .. module_name .. "." .. var_name .. " = " .. tostring(value))
                    return value
                end
                return nil
            end
            
            -- 添加设置其他模块变量的方法
            module.set_to = function(module_name, var_name, value)
                if _G.MODULES[module_name] then
                    _G.MODULES[module_name][var_name] = value
                    print(name .. " 设置 " .. module_name .. "." .. var_name .. " = " .. tostring(value))
                end
            end
            
            -- 添加直接访问其他模块的属性
            local mt = getmetatable(module) or {}
            local old_index = mt.__index
            
            mt.__index = function(t, key)
                -- 如果是访问其他模块，格式：modulename_varname
                if type(key) == "string" and key:find("_") then
                    local parts = {}
                    for part in key:gmatch("[^_]+") do
                        table.insert(parts, part)
                    end
                    
                    if #parts >= 2 then
                        local target_module = parts[1]
                        local var_name = table.concat(parts, "_", 2)
                        
                        if _G.MODULES[target_module] and _G.MODULES[target_module][var_name] then
                            return _G.MODULES[target_module][var_name]
                        end
                    end
                end
                
                -- 原有的__index逻辑
                if old_index then
                    if type(old_index) == "function" then
                        return old_index(t, key)
                    else
                        return old_index[key]
                    end
                end
                
                return rawget(t, key)
            end
            
            setmetatable(module, mt)
        end
    end
    
    print("已启用所有模块的跨模块访问功能")
end

-- 批量导入模块
function GlobalModuleManager.batch_import(module_configs)
    print("=== 批量导入模块 ===")
    
    for _, config in ipairs(module_configs) do
        local name = config.name
        local path = config.path
        local auto_register = config.auto_register ~= false  -- 默认为true
        
        print("导入模块: " .. name .. " 从 " .. path)
        
        local success, module = pcall(dofile, path)
        if success then
            if auto_register then
                GlobalModuleManager.register(name, module)
            end
        else
            print("导入失败: " .. tostring(module))
        end
    end
    
    -- 启用跨模块访问
    GlobalModuleManager.enable_cross_access()
end

-- 创建全局访问代理
function GlobalModuleManager.create_global_proxy()
    local proxy = {}
    
    local mt = {
        __index = function(t, key)
            return _G.MODULES[key]
        end,
        
        __newindex = function(t, key, value)
            if type(value) == "table" then
                GlobalModuleManager.register(key, value)
            else
                error("只能注册表类型的模块")
            end
        end
    }
    
    setmetatable(proxy, mt)
    return proxy
end

-- 模块间通信系统
function GlobalModuleManager.setup_communication()
    -- 创建全局事件系统
    _G.MODULE_EVENTS = {}
    
    -- 发送事件
    _G.emit_event = function(event_name, data, sender)
        if _G.MODULE_EVENTS[event_name] then
            for _, handler in ipairs(_G.MODULE_EVENTS[event_name]) do
                handler(data, sender)
            end
        end
    end
    
    -- 监听事件
    _G.on_event = function(event_name, handler)
        if not _G.MODULE_EVENTS[event_name] then
            _G.MODULE_EVENTS[event_name] = {}
        end
        table.insert(_G.MODULE_EVENTS[event_name], handler)
    end
    
    -- 为所有模块添加事件功能
    for name, module in pairs(_G.MODULES) do
        module.emit = function(event_name, data)
            _G.emit_event(event_name, data, name)
        end
        
        module.on = function(event_name, handler)
            _G.on_event(event_name, handler)
        end
    end
    
    print("模块间通信系统已设置")
end

return GlobalModuleManager
