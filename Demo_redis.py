# 以别名dcload
import time

import redis
from time import sleep
import AteRedis as CommonUser
from ATE_WriteLog import __writeToLog as __writeToLog
from datetime import datetime

import json

conWriteerr=11  #写异常
conReadErr=22   #读异常
conFuncErr=33  #抛出异常

ConFileName='AteRedis'



# varDataMode切换未不同的设备空能
def __GetDevJson(vardeviceAliase,varDataMode):
    try:
        # 构造消息数据
        scriptName = 'S7200.py'
        data = {
            "script": scriptName,
            "method": "set",
            "Command": "run_device",
            "args": {"arg1":"1.3","arg2":12,"arg3":"CV"},
            "request_id": "uuid-1234",
            "timeout": 1,
            "delay": 100
        }
        # 将数据放入列表中
        requests = [data]
        # 转换为JSON字符串
        json_string = json.dumps(requests, indent=4)
        return json_string
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__GetDevJson error ' + errorStr,ConFileName)
        return None

def __pubAndSubToRedis(session,sessioncmd,jsonstr,vardevName):
    try:
        # do something to redis
        idevName = vardevName  # 'S7200'

        # 安装依赖：pip install redis prettytable
        # r = CommonUser.__get_redis_connection()

        # print('josnSSSSSSS_', datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
        channel = ["device:ack:" + idevName, "device:response:" + idevName]
        channelx = ["device:ack:" + idevName, "device:response:" + idevName]
        channel = ["device:ack:" + idevName]
        channelx = ["device:ack:" + idevName]
        chmsg = {'ackStatus': '', 'responseStatus': ''}
        print(channel,channelx,chmsg)
        CommonUser.__subscribe_channel_async2(sessioncmd, channel, channelx, chmsg)  # 异步检测
        # sleep(1)
        # 发布前打印
        channelpublish = "device:cmd:" + idevName
        for i in range(1):
            # r = __get_redis_connection()
            # __subscribe_channel_async(r, "device:cmd:S7200")
            # sleep(1)
            formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
            # print('start_', formatted_time)
            # print_redis_data(r2)
            CommonUser.__publish_message(session, channelpublish, jsonstr)
            # 发布后打印
            formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
            print('start22_', formatted_time)
            # __writeToLog('start22_')
        istart_time = datetime.now()
        itime_diff = 0
        iHas = 0
        while (len(channelx) > 0) and (itime_diff <= 10):
            sleep(0.005)
            iend_time = datetime.now()
            itime_diff = iend_time - istart_time
            itime_diff = itime_diff.total_seconds()
        # print('josnendddddddd_', datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
        if (len(channelx) <= 0):
            return (conFuncErr, '', False)
        else:
            return (conFuncErr, '', True)

    except Exception as e:
        errorStr = str(e)
        __writeToLog('__GetRedisRespose error ' + errorStr,ConFileName)
        return (conFuncErr, '', False)

def exsample():
    retRedis = CommonUser.__get_redis_connection()
    retRedis2 = CommonUser.__get_redis_connection()
    idevName = 'DCLoad'  #别名
    channel = ["device:ack:" + idevName, "device:response:" + idevName]
    rr = CommonUser.__subscribe_channel(retRedis2, channel)  # 订阅
    for i in range(3):
        #DC_Load_ON(idevName, retRedis, None, None)
        #rr = CommonUser.__subscribe_channel(retRedis, channel)  # 异步检测
        istr=__GetDevJson('','')
        iret=__pubAndSubToRedis(retRedis,rr,istr,idevName)
        print(iret)
        #CommonUser.__Unsubscribe_channel(rr,channel)

    sleep(2)
    CommonUser.RedisClose(retRedis)

def exsample2():
    args={}
    args['OVP'] = 11
    args['OCP'] =11
    args['OPP'] = 11


    DataSet = {}
    DataSet['script'] = '22.py'
    DataSet['method'] = 'set'
    DataSet['request_id'] = "uuid-1234"
    DataSet['timeout'] = 1
    DataSet['delay'] = 100


    DataSet['Command'] = 'ParasSet_ProtectData'
    DataSet['args'] = args

    print(DataSet)


    requests = [DataSet]
    # 转换为JSON字符串
    json_string = json.dumps(requests, indent=4)
    print(json_string)
exsample2()


from dateutil.parser import parse
dt = parse('2025-06-30T18:56:23.4919217+08:00')
print(type(dt),dt)