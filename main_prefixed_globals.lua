-- 主脚本 - 带前缀的全局变量版本
-- 使用前缀避免命名冲突，但仍然是全局变量

print("=== 主脚本带前缀全局变量版本 ===\n")

-- 方案2：带前缀的全局变量（推荐）
MAIN_A = 10
MAIN_B = 20
MAIN_C = 30
MAIN_NAME = "main_prefixed_globals"

-- 同时创建无前缀的别名（可选）
A = MAIN_A
B = MAIN_B
C = MAIN_C

print("设置了带前缀的全局变量:")
print("MAIN_A = " .. MAIN_A)
print("MAIN_B = " .. MAIN_B)
print("MAIN_C = " .. MAIN_C)

print("\n同时创建了无前缀别名:")
print("A = " .. A)
print("B = " .. B)
print("C = " .. C)

-- 同步函数
function sync_aliases()
    A = MAIN_A
    B = MAIN_B
    C = MAIN_C
end

-- 更新函数
function update_main_vars(new_A, new_B, new_C)
    if new_A then
        MAIN_A = new_A
        A = MAIN_A
    end
    if new_B then
        MAIN_B = new_B
        B = MAIN_B
    end
    if new_C then
        MAIN_C = new_C
        C = MAIN_C
    end
end

-- 加载其他模块
print("\n加载其他模块...")
local other = dofile("other_prefixed.lua")
local third = dofile("third_prefixed.lua")

-- 在main中使用（两种方式都可以）
print("\n在main中使用全局变量:")

-- 方式1：使用带前缀的变量（明确来源）
print("使用带前缀的变量:")
local sum1 = MAIN_A + MAIN_B + MAIN_C
print("MAIN_A + MAIN_B + MAIN_C = " .. sum1)

-- 方式2：使用无前缀的别名（简洁）
print("使用无前缀别名:")
local sum2 = A + B + C
print("A + B + C = " .. sum2)

-- 修改变量
print("\n修改变量:")
update_main_vars(100, 200, 300)
print("更新后: MAIN_A=" .. MAIN_A .. ", MAIN_B=" .. MAIN_B .. ", MAIN_C=" .. MAIN_C)
print("别名: A=" .. A .. ", B=" .. B .. ", C=" .. C)

-- 函数中使用
function main_calculate()
    print("\nmain_calculate函数:")
    
    -- 可以使用两种方式
    local result1 = MAIN_A * 2 + MAIN_B * 3 + MAIN_C * 4
    local result2 = A * 2 + B * 3 + C * 4
    
    print("使用前缀: " .. result1)
    print("使用别名: " .. result2)
    
    return result1
end

main_calculate()

-- 与其他模块交互
print("\n与其他模块交互:")

-- 访问其他模块的变量
if other then
    print("访问other模块变量:")
    print("OTHER_X = " .. OTHER_X)
    print("OTHER_Y = " .. OTHER_Y)
    
    -- 跨模块计算
    local cross_calc = MAIN_A + OTHER_X
    print("MAIN_A + OTHER_X = " .. cross_calc)
end

if third then
    print("访问third模块变量:")
    print("THIRD_X = " .. THIRD_X)
    print("THIRD_Y = " .. THIRD_Y)
    
    -- 跨模块计算
    local cross_calc = MAIN_B + THIRD_X
    print("MAIN_B + THIRD_X = " .. cross_calc)
end

-- 全局变量总览
function print_all_globals()
    print("\n=== 所有全局变量总览 ===")
    
    print("主脚本变量:")
    print("  MAIN_A = " .. MAIN_A)
    print("  MAIN_B = " .. MAIN_B)
    print("  MAIN_C = " .. MAIN_C)
    
    if OTHER_X then
        print("Other模块变量:")
        print("  OTHER_X = " .. OTHER_X)
        print("  OTHER_Y = " .. OTHER_Y)
        print("  OTHER_Z = " .. OTHER_Z)
    end
    
    if THIRD_X then
        print("Third模块变量:")
        print("  THIRD_X = " .. THIRD_X)
        print("  THIRD_Y = " .. THIRD_Y)
        print("  THIRD_Z = " .. THIRD_Z)
    end
    
    print("无前缀别名:")
    print("  A = " .. A)
    print("  B = " .. B)
    print("  C = " .. C)
end

print_all_globals()

-- 批量操作所有模块的变量
function batch_global_operations()
    print("\n=== 批量操作所有全局变量 ===")
    
    -- 主脚本变量
    MAIN_A = MAIN_A + 10
    MAIN_B = MAIN_B + 20
    MAIN_C = MAIN_C + 30
    
    -- 其他模块变量
    if OTHER_X then
        OTHER_X = OTHER_X + 100
        OTHER_Y = OTHER_Y + 200
        OTHER_Z = OTHER_Z + 300
    end
    
    if THIRD_X then
        THIRD_X = THIRD_X + 1000
        THIRD_Y = THIRD_Y + 2000
        THIRD_Z = THIRD_Z + 3000
    end
    
    -- 同步别名
    sync_aliases()
    
    print("批量操作完成")
    print_all_globals()
end

batch_global_operations()

-- 总结
print("\n=== 总结 ===")
print("带前缀全局变量方案的优势:")
print("✅ 避免命名冲突（使用前缀）")
print("✅ 任何地方都可以直接访问")
print("✅ 可以创建无前缀别名简化使用")
print("✅ 变量来源清晰（通过前缀识别）")
print("✅ 支持跨模块直接访问")

print("\n使用方式:")
print("• 明确访问: MAIN_A, OTHER_X, THIRD_Y")
print("• 简化访问: A, B, C (通过别名)")
print("• 跨模块: 直接使用其他模块的前缀变量")

print("\n最终状态:")
print_all_globals()
