-- other模块 - lua测试序列例子
-- 使用 other.xxx 命名，避免与主脚本的 A, B, C 冲突
-- 可以直接使用主脚本的全局变量 A, B, C

print("=== Other模块加载中 ===")

-- 创建模块表
local other = {}

-- 模块变量（使用模块化命名）
other.X = 1000
other.Y = 2000
other.Z = 3000
other.name = "other_module"
other.version = "1.0"

print("Other模块变量初始化:")
print("  other.X = " .. other.X)
print("  other.Y = " .. other.Y)
print("  other.Z = " .. other.Z)

-- 模块基础函数
function other.print_variables()
    print("=== Other模块变量状态 ===")
    print("other.X = " .. other.X)
    print("other.Y = " .. other.Y)
    print("other.Z = " .. other.Z)
    print("other.name = " .. other.name)
end

function other.calculate()
    print("=== Other模块计算 ===")
    
    local sum = other.X + other.Y + other.Z
    local product = other.X * other.Y * other.Z
    local average = sum / 3
    
    print("other模块计算结果:")
    print("  sum = " .. sum)
    print("  product = " .. product)
    print("  average = " .. average)
    
    return {sum = sum, product = product, average = average}
end

-- 直接使用主脚本的全局变量
function other.use_main_vars()
    print("=== Other模块使用主脚本全局变量 ===")
    
    -- 直接使用 A, B, C（来自主脚本的全局变量）
    print("直接访问主脚本变量:")
    print("  A = " .. A)
    print("  B = " .. B)
    print("  C = " .. C)
    
    -- 混合计算（主脚本变量 + 模块变量）
    local mixed_calc1 = A + other.X
    local mixed_calc2 = B * other.Y / 1000
    local mixed_calc3 = C + other.Z / 100
    
    print("混合计算:")
    print("  A + other.X = " .. mixed_calc1)
    print("  B * other.Y / 1000 = " .. mixed_calc2)
    print("  C + other.Z / 100 = " .. mixed_calc3)
    
    -- 修改主脚本的全局变量
    print("Other模块修改主脚本变量:")
    A = A + 5
    B = B + 10
    C = C + 15
    print("  修改后 A = " .. A)
    print("  修改后 B = " .. B)
    print("  修改后 C = " .. C)
    
    return mixed_calc1 + mixed_calc2 + mixed_calc3
end

-- 复杂的数据处理
function other.data_processing()
    print("=== Other模块数据处理 ===")
    
    -- 使用主脚本变量进行条件判断
    if A > 50 then
        other.X = other.X * 1.1
        print("A > 50，增强 other.X = " .. other.X)
    end
    
    if B > 30 then
        other.Y = other.Y * 1.05
        print("B > 30，调整 other.Y = " .. other.Y)
    end
    
    -- 动态调整策略
    local ratio = (A + B + C) / (other.X + other.Y + other.Z)
    print("主脚本与other模块比例: " .. ratio)
    
    if ratio > 0.1 then
        print("比例偏高，调整other模块变量")
        other.X = other.X * 0.95
        other.Y = other.Y * 0.95
        other.Z = other.Z * 0.95
    elseif ratio < 0.05 then
        print("比例偏低，增强other模块变量")
        other.X = other.X * 1.05
        other.Y = other.Y * 1.05
        other.Z = other.Z * 1.05
    end
    
    print("处理后 other变量:")
    print("  other.X = " .. other.X)
    print("  other.Y = " .. other.Y)
    print("  other.Z = " .. other.Z)
    
    return ratio
end

-- 与third模块协作
function other.collaborate_with_third()
    print("=== Other与Third模块协作 ===")
    
    if _G.third then
        print("协作前状态:")
        print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
        print("  third: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
        
        -- 数据交换和同步
        local temp_A = A
        A = (other.X + third.X) / 2000  -- 取两个模块X的平均值并缩放
        
        local temp_other_X = other.X
        other.X = temp_A * 100 + third.Y / 10
        
        third.Y = temp_other_X / 100 + B * 10
        
        print("协作交换完成")
        print("协作后状态:")
        print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
        print("  third: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
        
        -- 联合计算
        local joint_result = A + other.X + third.X
        print("联合计算结果: " .. joint_result)
        
        return joint_result
    else
        print("Third模块未找到，无法协作")
        return A + other.X
    end
end

-- 模拟业务逻辑
function other.business_logic()
    print("=== Other模块业务逻辑 ===")
    
    -- 阶段1：数据验证
    print("阶段1: 数据验证")
    local valid = true
    
    if A <= 0 or B <= 0 or C <= 0 then
        print("  ⚠️ 主脚本数据异常")
        valid = false
    end
    
    if other.X <= 0 or other.Y <= 0 or other.Z <= 0 then
        print("  ⚠️ Other模块数据异常")
        valid = false
    end
    
    if valid then
        print("  ✅ 数据验证通过")
    else
        print("  ❌ 数据验证失败，执行修复")
        A = math.max(A, 1)
        B = math.max(B, 1)
        C = math.max(C, 1)
        other.X = math.max(other.X, 1)
        other.Y = math.max(other.Y, 1)
        other.Z = math.max(other.Z, 1)
    end
    
    -- 阶段2：业务计算
    print("阶段2: 业务计算")
    local efficiency = (A * B * C) / (other.X * other.Y * other.Z) * 1000000
    print("  效率指标: " .. efficiency)
    
    -- 阶段3：优化调整
    print("阶段3: 优化调整")
    if efficiency > 1.0 then
        print("  效率良好，保持当前参数")
    else
        print("  效率偏低，执行优化")
        A = A * 1.1
        B = B * 1.1
        other.X = other.X * 0.9
        other.Y = other.Y * 0.9
        print("  优化后 A=" .. A .. ", B=" .. B)
        print("  优化后 other.X=" .. other.X .. ", other.Y=" .. other.Y)
    end
    
    return efficiency
end

-- 批量操作接口
function other.batch_operations(operations)
    print("=== Other模块批量操作 ===")
    
    for i, op in ipairs(operations) do
        if op.target == "main" then
            -- 操作主脚本的全局变量
            if op.var == "A" and op.type == "add" then
                A = A + op.value
            elseif op.var == "B" and op.type == "multiply" then
                B = B * op.value
            elseif op.var == "C" and op.type == "set" then
                C = op.value
            end
            print("操作" .. i .. ": " .. op.type .. " 主脚本." .. op.var .. " " .. op.value)
            
        elseif op.target == "other" then
            -- 操作other模块变量
            if op.var == "X" and op.type == "add" then
                other.X = other.X + op.value
            elseif op.var == "Y" and op.type == "multiply" then
                other.Y = other.Y * op.value
            elseif op.var == "Z" and op.type == "set" then
                other.Z = op.value
            end
            print("操作" .. i .. ": " .. op.type .. " other." .. op.var .. " " .. op.value)
        end
    end
    
    print("批量操作完成")
end

-- 获取模块状态
function other.get_status()
    return {
        module_vars = {
            X = other.X,
            Y = other.Y,
            Z = other.Z,
            name = other.name
        },
        main_vars = {
            A = A,
            B = B,
            C = C
        },
        timestamp = os.time()
    }
end

-- 模块自检
function other.self_check()
    print("=== Other模块自检 ===")
    
    local status = other.get_status()
    
    print("模块变量检查:")
    for k, v in pairs(status.module_vars) do
        if type(v) == "number" then
            print("  " .. k .. " = " .. v .. " (" .. (v > 0 and "正常" or "异常") .. ")")
        else
            print("  " .. k .. " = " .. tostring(v))
        end
    end
    
    print("主脚本变量检查:")
    for k, v in pairs(status.main_vars) do
        print("  " .. k .. " = " .. v .. " (" .. (v > 0 and "正常" or "异常") .. ")")
    end
    
    print("✅ Other模块自检完成")
    return status
end

print("✅ Other模块加载完成")

-- 返回模块表
return other
