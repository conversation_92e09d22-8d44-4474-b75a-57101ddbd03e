-- 简单的全局模块访问演示
-- 最直接的解决方案

print("=== 简单全局模块访问演示 ===\n")

-- 1. 在main中统一加载所有模块到全局变量
print("1. 统一加载模块到全局变量:")

-- 加载模块并设置为全局变量
_G.other = dofile("other_global.lua")
_G.third = dofile("third_global.lua")

print("✅ other模块已加载到全局变量")
print("✅ third模块已加载到全局变量")

-- 2. 验证全局访问
print("\n2. 验证全局访问:")
print("全局 other.A = " .. _G.other.A)
print("全局 third.X = " .. _G.third.X)

-- 也可以直接使用（因为已经是全局变量）
print("直接 other.B = " .. other.B)
print("直接 third.Y = " .. third.Y)

-- 3. 在main中使用 other.xxxvar 形式
print("\n3. 在main中使用模块变量:")

-- 读取变量
local value1 = other.A
local value2 = third.X
print("读取 other.A = " .. value1)
print("读取 third.X = " .. value2)

-- 修改变量
other.A = 555
third.X = 777
print("修改后 other.A = " .. other.A)
print("修改后 third.X = " .. third.X)

-- 4. 模块间直接访问演示
print("\n4. 模块间直接访问:")

-- other模块现在可以直接访问third模块
print("other模块访问third模块:")
other.use_third_variables()

print("\nthird模块访问other模块:")
third.use_other_variables()

-- 5. 复杂操作
print("\n5. 复杂跨模块操作:")
local result1 = other.collaborate_with_third()
local result2 = third.complex_interaction()

-- 6. 联合计算
print("\n6. 联合计算示例:")
local function joint_calculation()
    -- 直接使用所有模块的变量
    local sum = other.A + other.B + other.C + third.X + third.Y + third.Z
    local product = other.A * third.X
    local ratio = other.B / third.Y
    
    print("联合计算结果:")
    print("  总和: " .. sum)
    print("  乘积: " .. product)
    print("  比率: " .. ratio)
    
    return {sum = sum, product = product, ratio = ratio}
end

local calc_result = joint_calculation()

-- 7. 动态模块创建
print("\n7. 动态创建新模块:")
_G.fourth = {
    P = 100,
    Q = 200,
    R = 300,
    
    print_vars = function()
        print("fourth.P = " .. _G.fourth.P)
        print("fourth.Q = " .. _G.fourth.Q)
        print("fourth.R = " .. _G.fourth.R)
    end,
    
    use_other_modules = function()
        print("fourth模块访问其他模块:")
        print("  other.A = " .. other.A)
        print("  third.X = " .. third.X)
        
        -- 修改其他模块
        other.C = other.C + 10
        third.Z = third.Z + 20
        
        print("fourth模块修改了其他模块的变量")
    end
}

print("✅ 动态创建了fourth模块")
fourth.print_vars()
fourth.use_other_modules()

-- 8. 所有模块状态
print("\n8. 所有模块最终状态:")
print("=== other模块 ===")
other.print_variables()

print("\n=== third模块 ===")
third.print_variables()

print("\n=== fourth模块 ===")
fourth.print_vars()

-- 9. 验证模块间的相互访问
print("\n9. 验证模块间相互访问:")

-- 创建一个测试函数，验证所有模块都能访问彼此
local function test_cross_access()
    print("测试跨模块访问:")
    
    -- other访问third
    local other_sees_third = other.get_from and other.get_from("third", "X") or third.X
    print("other看到的third.X = " .. other_sees_third)
    
    -- third访问other
    local third_sees_other = third.get_from and third.get_from("other", "A") or other.A
    print("third看到的other.A = " .. third_sees_other)
    
    -- fourth访问所有
    print("fourth看到的所有变量:")
    print("  other.A = " .. other.A)
    print("  other.B = " .. other.B)
    print("  third.X = " .. third.X)
    print("  third.Y = " .. third.Y)
    
    return true
end

local access_test = test_cross_access()

-- 10. 总结
print("\n=== 总结 ===")
print("✅ 解决方案验证成功！")
print("")
print("实现的功能:")
print("1. ✅ 在main中统一导入所有模块")
print("2. ✅ 支持 other.xxxvar = xx 语法")
print("3. ✅ 支持 yy = other.xxxvar 语法")
print("4. ✅ 模块间可以直接访问，无需单独导入")
print("5. ✅ 所有操作在同一个Lua环境中")
print("6. ✅ 支持动态添加新模块")
print("7. ✅ 支持复杂的跨模块操作")
print("")
print("关键原理:")
print("• 在main中将模块加载为全局变量")
print("• 所有模块共享同一个Lua环境")
print("• 模块可以通过全局变量直接访问彼此")
print("• 无需在每个模块中单独导入其他模块")
print("")
print("使用方式:")
print("• main: other.A = 100")
print("• main: value = third.X")
print("• other模块内: third.Y = 200")
print("• third模块内: other.B = 300")

print("\n演示完成！")
