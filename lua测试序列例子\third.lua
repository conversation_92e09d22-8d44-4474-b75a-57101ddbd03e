-- third模块 - lua测试序列例子
-- 使用 third.xxx 命名，避免与主脚本的 A, B, C 冲突
-- 可以直接使用主脚本的全局变量 A, B, C

print("=== Third模块加载中 ===")

-- 创建模块表
local third = {}

-- 模块变量（使用模块化命名）
third.X = 5000
third.Y = 6000
third.Z = 7000
third.name = "third_module"
third.version = "1.0"

print("Third模块变量初始化:")
print("  third.X = " .. third.X)
print("  third.Y = " .. third.Y)
print("  third.Z = " .. third.Z)

-- 模块基础函数
function third.print_variables()
    print("=== Third模块变量状态 ===")
    print("third.X = " .. third.X)
    print("third.Y = " .. third.Y)
    print("third.Z = " .. third.Z)
    print("third.name = " .. third.name)
end

function third.calculate()
    print("=== Third模块计算 ===")
    
    local sum = third.X + third.Y + third.Z
    local product = third.X * third.Y * third.Z
    local average = sum / 3
    
    print("third模块计算结果:")
    print("  sum = " .. sum)
    print("  product = " .. product)
    print("  average = " .. average)
    
    return {sum = sum, product = product, average = average}
end

-- 直接使用主脚本的全局变量
function third.use_main_vars()
    print("=== Third模块使用主脚本全局变量 ===")
    
    -- 直接使用 A, B, C（来自主脚本的全局变量）
    print("直接访问主脚本变量:")
    print("  A = " .. A)
    print("  B = " .. B)
    print("  C = " .. C)
    
    -- 混合计算（主脚本变量 + 模块变量）
    local mixed_calc1 = A * third.X / 1000
    local mixed_calc2 = B + third.Y / 100
    local mixed_calc3 = C * third.Z / 1000
    
    print("混合计算:")
    print("  A * third.X / 1000 = " .. mixed_calc1)
    print("  B + third.Y / 100 = " .. mixed_calc2)
    print("  C * third.Z / 1000 = " .. mixed_calc3)
    
    -- 修改主脚本的全局变量
    print("Third模块修改主脚本变量:")
    A = A + 2
    B = B + 4
    C = C + 6
    print("  修改后 A = " .. A)
    print("  修改后 B = " .. B)
    print("  修改后 C = " .. C)
    
    return mixed_calc1 + mixed_calc2 + mixed_calc3
end

-- 高级数学运算
function third.advanced_math()
    print("=== Third模块高级数学运算 ===")
    
    -- 使用主脚本变量和模块变量进行高级计算
    print("输入数据:")
    print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
    print("  third模块: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
    
    -- 几何平均数
    local geo_mean_main = (A * B * C) ^ (1/3)
    local geo_mean_third = (third.X * third.Y * third.Z) ^ (1/3)
    
    print("几何平均数:")
    print("  主脚本: " .. geo_mean_main)
    print("  third模块: " .. geo_mean_third)
    
    -- 调和平均数
    local harm_mean_main = 3 / (1/A + 1/B + 1/C)
    local harm_mean_third = 3 / (1/third.X + 1/third.Y + 1/third.Z)
    
    print("调和平均数:")
    print("  主脚本: " .. harm_mean_main)
    print("  third模块: " .. harm_mean_third)
    
    -- 标准差计算
    local mean_main = (A + B + C) / 3
    local std_dev_main = math.sqrt(((A - mean_main)^2 + (B - mean_main)^2 + (C - mean_main)^2) / 3)
    
    print("主脚本标准差: " .. std_dev_main)
    
    -- 相关性分析
    local correlation = (A * third.X + B * third.Y + C * third.Z) / 
                       (math.sqrt(A^2 + B^2 + C^2) * math.sqrt(third.X^2 + third.Y^2 + third.Z^2))
    
    print("交叉相关性: " .. correlation)
    
    return {
        geo_main = geo_mean_main,
        geo_third = geo_mean_third,
        harm_main = harm_mean_main,
        harm_third = harm_mean_third,
        std_dev = std_dev_main,
        correlation = correlation
    }
end

-- 模拟物理系统
function third.physics_simulation()
    print("=== Third模块物理系统模拟 ===")
    
    -- 将变量映射到物理量
    local mass = A        -- 质量（来自主脚本）
    local velocity = B    -- 速度（来自主脚本）
    local time = C        -- 时间（来自主脚本）
    
    local force_x = third.X / 1000    -- X方向力
    local force_y = third.Y / 1000    -- Y方向力
    local force_z = third.Z / 1000    -- Z方向力
    
    print("物理参数:")
    print("  质量: " .. mass)
    print("  初始速度: " .. velocity)
    print("  时间: " .. time)
    print("  力 (X, Y, Z): " .. force_x .. ", " .. force_y .. ", " .. force_z)
    
    -- 计算加速度 (F = ma)
    local accel_x = force_x / mass
    local accel_y = force_y / mass
    local accel_z = force_z / mass
    
    print("加速度 (X, Y, Z): " .. accel_x .. ", " .. accel_y .. ", " .. accel_z)
    
    -- 计算位移 (s = vt + 0.5at²)
    local displacement_x = velocity * time + 0.5 * accel_x * time^2
    local displacement_y = velocity * time + 0.5 * accel_y * time^2
    local displacement_z = velocity * time + 0.5 * accel_z * time^2
    
    print("位移 (X, Y, Z): " .. displacement_x .. ", " .. displacement_y .. ", " .. displacement_z)
    
    -- 计算总位移
    local total_displacement = math.sqrt(displacement_x^2 + displacement_y^2 + displacement_z^2)
    print("总位移: " .. total_displacement)
    
    -- 更新主脚本变量（模拟状态变化）
    A = mass  -- 质量不变
    B = velocity + (accel_x + accel_y + accel_z) * time / 3  -- 平均加速后的速度
    C = time + 1  -- 时间推进
    
    print("更新后的主脚本变量:")
    print("  A (质量) = " .. A)
    print("  B (速度) = " .. B)
    print("  C (时间) = " .. C)
    
    return total_displacement
end

-- 数据分析和优化
function third.data_analysis()
    print("=== Third模块数据分析 ===")
    
    -- 收集所有数据
    local main_data = {A, B, C}
    local third_data = {third.X, third.Y, third.Z}
    local other_data = {}
    
    if _G.other then
        other_data = {other.X, other.Y, other.Z}
    end
    
    print("数据收集:")
    print("  主脚本数据: " .. table.concat(main_data, ", "))
    print("  third数据: " .. table.concat(third_data, ", "))
    if #other_data > 0 then
        print("  other数据: " .. table.concat(other_data, ", "))
    end
    
    -- 统计分析
    local main_sum = A + B + C
    local third_sum = third.X + third.Y + third.Z
    local total_sum = main_sum + third_sum
    
    if #other_data > 0 then
        local other_sum = other.X + other.Y + other.Z
        total_sum = total_sum + other_sum
        print("other模块贡献: " .. string.format("%.1f%%", other_sum / total_sum * 100))
    end
    
    print("贡献分析:")
    print("  主脚本贡献: " .. string.format("%.1f%%", main_sum / total_sum * 100))
    print("  third模块贡献: " .. string.format("%.1f%%", third_sum / total_sum * 100))
    
    -- 优化建议
    print("优化建议:")
    if main_sum / total_sum > 0.6 then
        print("  主脚本权重过高，建议增强模块功能")
        third.X = third.X * 1.2
        third.Y = third.Y * 1.2
    elseif main_sum / total_sum < 0.2 then
        print("  主脚本权重过低，建议优化主脚本参数")
        A = A * 1.1
        B = B * 1.1
        C = C * 1.1
    else
        print("  数据分布合理，保持当前配置")
    end
    
    return {
        main_contribution = main_sum / total_sum,
        third_contribution = third_sum / total_sum,
        total_sum = total_sum
    }
end

-- 与other模块的协作接口
function third.collaborate_with_other()
    print("=== Third与Other模块协作 ===")
    
    if _G.other then
        print("三方协作状态:")
        print("  主脚本: A=" .. A .. ", B=" .. B .. ", C=" .. C)
        print("  other: X=" .. other.X .. ", Y=" .. other.Y .. ", Z=" .. other.Z)
        print("  third: X=" .. third.X .. ", Y=" .. third.Y .. ", Z=" .. third.Z)
        
        -- 协作算法
        local main_factor = (A + B + C) / 3
        local other_factor = (other.X + other.Y + other.Z) / 3
        local third_factor = (third.X + third.Y + third.Z) / 3
        
        print("协作因子:")
        print("  主脚本因子: " .. main_factor)
        print("  other因子: " .. other_factor)
        print("  third因子: " .. third_factor)
        
        -- 动态平衡调整
        local total_factor = main_factor + other_factor + third_factor
        local target_ratio = 1/3  -- 目标是三方平衡
        
        if main_factor / total_factor > target_ratio + 0.1 then
            print("主脚本因子过高，执行平衡调整")
            A = A * 0.9
            B = B * 0.9
            C = C * 0.9
        end
        
        if other_factor / total_factor > target_ratio + 0.1 then
            print("other因子过高，执行平衡调整")
            other.X = other.X * 0.9
            other.Y = other.Y * 0.9
        end
        
        if third_factor / total_factor > target_ratio + 0.1 then
            print("third因子过高，执行平衡调整")
            third.X = third.X * 0.9
            third.Y = third.Y * 0.9
        end
        
        print("平衡调整完成")
        return total_factor
    else
        print("Other模块未找到，无法进行协作")
        return A + B + C + third.X + third.Y + third.Z
    end
end

-- 获取模块状态
function third.get_status()
    return {
        module_vars = {
            X = third.X,
            Y = third.Y,
            Z = third.Z,
            name = third.name
        },
        main_vars = {
            A = A,
            B = B,
            C = C
        },
        timestamp = os.time()
    }
end

-- 模块自检
function third.self_check()
    print("=== Third模块自检 ===")
    
    local status = third.get_status()
    
    print("模块变量检查:")
    for k, v in pairs(status.module_vars) do
        if type(v) == "number" then
            print("  " .. k .. " = " .. v .. " (" .. (v > 0 and "正常" or "异常") .. ")")
        else
            print("  " .. k .. " = " .. tostring(v))
        end
    end
    
    print("主脚本变量检查:")
    for k, v in pairs(status.main_vars) do
        print("  " .. k .. " = " .. v .. " (" .. (v > 0 and "正常" or "异常") .. ")")
    end
    
    print("✅ Third模块自检完成")
    return status
end

print("✅ Third模块加载完成")

-- 返回模块表
return third
