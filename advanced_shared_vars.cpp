#include <lua.hpp>
#include <iostream>
#include <unordered_map>
#include <string>
#include <memory>
#include <vector>

// 高级共享变量系统
class AdvancedSharedVars {
private:
    static std::unordered_map<std::string, lua_Number> numeric_vars;
    static std::unordered_map<std::string, std::string> string_vars;
    static std::unordered_map<std::string, bool> boolean_vars;
    static std::vector<lua_State*> registered_states;

public:
    // 注册Lua状态
    static void register_state(lua_State* L) {
        registered_states.push_back(L);
    }

    // 同步变量到所有注册的状态
    static void sync_to_all_states(const std::string& key) {
        for (lua_State* L : registered_states) {
            sync_var_to_state(L, key);
        }
    }

    // 同步单个变量到指定状态
    static void sync_var_to_state(lua_State* L, const std::string& key) {
        if (numeric_vars.find(key) != numeric_vars.end()) {
            lua_pushnumber(L, numeric_vars[key]);
            lua_setglobal(L, key.c_str());
        } else if (string_vars.find(key) != string_vars.end()) {
            lua_pushstring(L, string_vars[key].c_str());
            lua_setglobal(L, key.c_str());
        } else if (boolean_vars.find(key) != boolean_vars.end()) {
            lua_pushboolean(L, boolean_vars[key]);
            lua_setglobal(L, key.c_str());
        }
    }

    // 设置数值变量
    static void set_number(const std::string& key, lua_Number value) {
        numeric_vars[key] = value;
        sync_to_all_states(key);
        std::cout << "设置数值变量 " << key << " = " << value << std::endl;
    }

    // 设置字符串变量
    static void set_string(const std::string& key, const std::string& value) {
        string_vars[key] = value;
        sync_to_all_states(key);
        std::cout << "设置字符串变量 " << key << " = " << value << std::endl;
    }

    // 设置布尔变量
    static void set_boolean(const std::string& key, bool value) {
        boolean_vars[key] = value;
        sync_to_all_states(key);
        std::cout << "设置布尔变量 " << key << " = " << (value ? "true" : "false") << std::endl;
    }
};

// 静态成员定义
std::unordered_map<std::string, lua_Number> AdvancedSharedVars::numeric_vars;
std::unordered_map<std::string, std::string> AdvancedSharedVars::string_vars;
std::unordered_map<std::string, bool> AdvancedSharedVars::boolean_vars;
std::vector<lua_State*> AdvancedSharedVars::registered_states;

// 高级 __index 元方法
static int advanced_index(lua_State* L) {
    const char* key = lua_tostring(L, 2);
    
    if (!key) {
        lua_pushnil(L);
        return 1;
    }

    std::string key_str(key);

    // 检查数值变量
    if (AdvancedSharedVars::numeric_vars.find(key_str) != AdvancedSharedVars::numeric_vars.end()) {
        lua_pushnumber(L, AdvancedSharedVars::numeric_vars[key_str]);
        return 1;
    }

    // 检查字符串变量
    if (AdvancedSharedVars::string_vars.find(key_str) != AdvancedSharedVars::string_vars.end()) {
        lua_pushstring(L, AdvancedSharedVars::string_vars[key_str].c_str());
        return 1;
    }

    // 检查布尔变量
    if (AdvancedSharedVars::boolean_vars.find(key_str) != AdvancedSharedVars::boolean_vars.end()) {
        lua_pushboolean(L, AdvancedSharedVars::boolean_vars[key_str]);
        return 1;
    }

    // 如果没找到，返回nil
    lua_pushnil(L);
    return 1;
}

// 高级 __newindex 元方法
static int advanced_newindex(lua_State* L) {
    const char* key = lua_tostring(L, 2);
    
    if (!key) return 0;

    std::string key_str(key);

    // 根据值的类型存储到相应的容器中
    if (lua_isnumber(L, 3)) {
        lua_Number value = lua_tonumber(L, 3);
        AdvancedSharedVars::set_number(key_str, value);
    } else if (lua_isstring(L, 3)) {
        const char* value = lua_tostring(L, 3);
        AdvancedSharedVars::set_string(key_str, std::string(value));
    } else if (lua_isboolean(L, 3)) {
        bool value = lua_toboolean(L, 3);
        AdvancedSharedVars::set_boolean(key_str, value);
    }

    return 0;
}

// Lua接口函数
static int register_lua_state(lua_State* L) {
    AdvancedSharedVars::register_state(L);
    std::cout << "Lua状态已注册到高级共享变量系统" << std::endl;
    return 0;
}

static int init_advanced_shared_vars(lua_State* L) {
    // 注册当前状态
    AdvancedSharedVars::register_state(L);

    // 创建元表
    luaL_newmetatable(L, "AdvancedSharedVarsMetatable");
    
    // 设置元方法
    lua_pushstring(L, "__index");
    lua_pushcfunction(L, advanced_index);
    lua_settable(L, -3);
    
    lua_pushstring(L, "__newindex");
    lua_pushcfunction(L, advanced_newindex);
    lua_settable(L, -3);
    
    // 设置到全局环境
    lua_getglobal(L, "_G");
    lua_pushvalue(L, -2);
    lua_setmetatable(L, -2);
    
    lua_pop(L, 2);
    
    std::cout << "高级共享变量系统已初始化" << std::endl;
    return 0;
}

static int print_all_vars(lua_State* L) {
    std::cout << "=== 所有共享变量 ===" << std::endl;
    
    std::cout << "数值变量:" << std::endl;
    for (const auto& pair : AdvancedSharedVars::numeric_vars) {
        std::cout << "  " << pair.first << " = " << pair.second << std::endl;
    }
    
    std::cout << "字符串变量:" << std::endl;
    for (const auto& pair : AdvancedSharedVars::string_vars) {
        std::cout << "  " << pair.first << " = \"" << pair.second << "\"" << std::endl;
    }
    
    std::cout << "布尔变量:" << std::endl;
    for (const auto& pair : AdvancedSharedVars::boolean_vars) {
        std::cout << "  " << pair.first << " = " << (pair.second ? "true" : "false") << std::endl;
    }
    
    return 0;
}

// 注册函数
static const luaL_Reg advanced_shared_vars_lib[] = {
    {"init", init_advanced_shared_vars},
    {"register_state", register_lua_state},
    {"print_all", print_all_vars},
    {NULL, NULL}
};

// 模块入口点
extern "C" int luaopen_advanced_shared_vars(lua_State* L) {
    luaL_newlib(L, advanced_shared_vars_lib);
    return 1;
}
