-- 代理模式的模块系统
-- 允许模块间透明的变量访问

local ProxyModules = {}

-- 全局模块注册表
local module_registry = {}
local proxy_cache = {}

-- 创建模块代理
function ProxyModules.create_proxy(module_name, target_module)
    if proxy_cache[module_name] then
        return proxy_cache[module_name]
    end
    
    local proxy = {}
    
    local mt = {
        __index = function(t, key)
            -- 如果是访问其他模块的变量，格式：other.varname
            if type(key) == "string" and key:find("%.") then
                local parts = {}
                for part in key:gmatch("[^%.]+") do
                    table.insert(parts, part)
                end
                
                if #parts == 2 then
                    local target_module_name = parts[1]
                    local var_name = parts[2]
                    
                    if module_registry[target_module_name] then
                        print("代理访问: " .. module_name .. " -> " .. target_module_name .. "." .. var_name)
                        return module_registry[target_module_name][var_name]
                    end
                end
            end
            
            -- 普通变量访问
            if target_module and target_module[key] then
                return target_module[key]
            end
            
            -- 如果是函数调用，创建代理函数
            if target_module and type(target_module[key]) == "function" then
                return function(...)
                    print("代理函数调用: " .. module_name .. "." .. key)
                    return target_module[key](...)
                end
            end
            
            return nil
        end,
        
        __newindex = function(t, key, value)
            -- 如果是设置其他模块的变量，格式：other.varname = value
            if type(key) == "string" and key:find("%.") then
                local parts = {}
                for part in key:gmatch("[^%.]+") do
                    table.insert(parts, part)
                end
                
                if #parts == 2 then
                    local target_module_name = parts[1]
                    local var_name = parts[2]
                    
                    if module_registry[target_module_name] then
                        print("代理设置: " .. module_name .. " -> " .. target_module_name .. "." .. var_name .. " = " .. tostring(value))
                        module_registry[target_module_name][var_name] = value
                        return
                    end
                end
            end
            
            -- 普通变量设置
            if target_module then
                target_module[key] = value
            end
        end,
        
        __call = function(t, ...)
            -- 使模块可以被调用
            print("模块 " .. module_name .. " 被调用")
            if target_module and target_module.main then
                return target_module.main(...)
            end
        end
    }
    
    setmetatable(proxy, mt)
    proxy_cache[module_name] = proxy
    
    return proxy
end

-- 注册模块
function ProxyModules.register_module(name, module)
    module_registry[name] = module
    print("模块 " .. name .. " 已注册到代理系统")
end

-- 创建智能模块（自动支持跨模块访问）
function ProxyModules.create_smart_module(name, initial_data)
    local module = initial_data or {}
    
    -- 添加特殊方法来访问其他模块
    module._get_from = function(module_name, var_name)
        if module_registry[module_name] then
            return module_registry[module_name][var_name]
        end
        return nil
    end
    
    module._set_to = function(module_name, var_name, value)
        if module_registry[module_name] then
            module_registry[module_name][var_name] = value
            print("跨模块设置: " .. name .. " -> " .. module_name .. "." .. var_name .. " = " .. tostring(value))
        end
    end
    
    module._copy_from = function(module_name, var_mapping)
        if module_registry[module_name] then
            for local_var, remote_var in pairs(var_mapping) do
                module[local_var] = module_registry[module_name][remote_var]
                print("复制变量: " .. module_name .. "." .. remote_var .. " -> " .. name .. "." .. local_var)
            end
        end
    end
    
    module._sync_with = function(module_name, var_mapping)
        if module_registry[module_name] then
            for local_var, remote_var in pairs(var_mapping) do
                -- 双向同步（这里简单地将本地值复制到远程）
                module_registry[module_name][remote_var] = module[local_var]
                print("同步变量: " .. name .. "." .. local_var .. " <-> " .. module_name .. "." .. remote_var)
            end
        end
    end
    
    -- 注册模块
    ProxyModules.register_module(name, module)
    
    -- 返回代理
    return ProxyModules.create_proxy(name, module)
end

-- 全局变量访问器
function ProxyModules.global_accessor()
    local accessor = {}
    
    local mt = {
        __index = function(t, key)
            -- 支持 accessor.module_name.var_name 的访问方式
            if module_registry[key] then
                return ProxyModules.create_proxy(key, module_registry[key])
            end
            return nil
        end
    }
    
    setmetatable(accessor, mt)
    return accessor
end

-- 批量跨模块操作
function ProxyModules.batch_cross_module_ops(operations)
    print("=== 执行批量跨模块操作 ===")
    
    for i, op in ipairs(operations) do
        if op.type == "copy" then
            -- 复制变量：{type="copy", from="module1.var1", to="module2.var2"}
            local from_parts = {}
            local to_parts = {}
            
            for part in op.from:gmatch("[^%.]+") do
                table.insert(from_parts, part)
            end
            for part in op.to:gmatch("[^%.]+") do
                table.insert(to_parts, part)
            end
            
            if #from_parts == 2 and #to_parts == 2 then
                local from_module, from_var = from_parts[1], from_parts[2]
                local to_module, to_var = to_parts[1], to_parts[2]
                
                if module_registry[from_module] and module_registry[to_module] then
                    local value = module_registry[from_module][from_var]
                    module_registry[to_module][to_var] = value
                    print("批量复制: " .. op.from .. " -> " .. op.to .. " (值: " .. tostring(value) .. ")")
                end
            end
            
        elseif op.type == "calculate" then
            -- 跨模块计算：{type="calculate", expr="module1.var1 + module2.var2", result="module3.var3"}
            -- 这里简化实现，实际可以用更复杂的表达式解析
            print("批量计算: " .. op.expr .. " -> " .. op.result)
        end
    end
end

-- 打印所有模块状态
function ProxyModules.print_all_modules()
    print("=== 所有注册模块状态 ===")
    for name, module in pairs(module_registry) do
        print("模块: " .. name)
        for key, value in pairs(module) do
            if type(value) ~= "function" and not key:match("^_") then
                print("  " .. key .. " = " .. tostring(value))
            end
        end
        print("")
    end
end

return ProxyModules
