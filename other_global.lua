-- other模块 - 全局访问版本
-- 不需要手动导入其他模块，可以直接访问全局注册的模块

local other = {}

-- 模块变量
other.A = 100
other.B = 200
other.C = 300
other.name = "other_global"
other.version = "2.0"

-- 模块函数
function other.print_variables()
    print("=== other_global模块变量 ===")
    print("other.A = " .. other.A)
    print("other.B = " .. other.B)
    print("other.C = " .. other.C)
end

function other.calculate()
    local sum = other.A + other.B + other.C
    local product = other.A * other.B * other.C
    
    print("other模块计算结果:")
    print("sum = " .. sum)
    print("product = " .. product)
    
    return {sum = sum, product = product}
end

-- 直接访问third模块的函数（无需导入）
function other.use_third_variables()
    print("=== other模块访问third模块 ===")
    
    -- 方法1：通过全局MODULES访问
    if _G.MODULES and _G.MODULES.third then
        local third = _G.MODULES.third
        print("通过MODULES访问 third.X = " .. third.X)
        print("通过MODULES访问 third.Y = " .. third.Y)
        
        -- 修改third模块的变量
        third.X = third.X + 50
        print("修改third.X为: " .. third.X)
    end
    
    -- 方法2：通过全局变量直接访问（如果已注册）
    if _G.third then
        print("通过全局变量访问 third.Z = " .. _G.third.Z)
        
        -- 调用third模块的函数
        if _G.third.calculate then
            print("调用third模块的计算函数:")
            _G.third.calculate()
        end
    end
    
    -- 方法3：使用跨模块访问方法（如果已启用）
    if other.get_from then
        local third_X = other.get_from("third", "X")
        print("通过get_from访问 third.X = " .. tostring(third_X))
        
        -- 设置third模块的变量
        other.set_to("third", "Y", 9999)
    end
end

-- 与third模块协作的函数
function other.collaborate_with_third()
    print("=== other与third协作 ===")
    
    if not _G.MODULES or not _G.MODULES.third then
        print("third模块未找到，无法协作")
        return
    end
    
    local third = _G.MODULES.third
    
    -- 数据交换
    local temp = other.A
    other.A = third.X
    third.X = temp
    
    print("交换变量: other.A <-> third.X")
    print("现在 other.A = " .. other.A)
    print("现在 third.X = " .. third.X)
    
    -- 联合计算
    local combined_result = other.A + other.B + third.Y + third.Z
    print("联合计算结果: " .. combined_result)
    
    return combined_result
end

-- 监听来自其他模块的事件
function other.setup_event_listeners()
    if _G.on_event then
        -- 监听third模块的变量变化事件
        _G.on_event("variable_changed", function(data, sender)
            if sender == "third" then
                print("other模块收到third模块的变量变化通知: " .. data.var .. " = " .. data.value)
                
                -- 根据third的变化调整自己的变量
                if data.var == "X" and data.value > 1000 then
                    other.A = other.A + 10
                    print("other.A 自动调整为: " .. other.A)
                end
            end
        end)
        
        -- 监听计算请求事件
        _G.on_event("calculate_request", function(data, sender)
            print("other模块收到来自 " .. sender .. " 的计算请求")
            local result = other.calculate()
            
            -- 发送计算结果
            if other.emit then
                other.emit("calculate_result", {
                    result = result,
                    from = "other"
                })
            end
        end)
    end
end

-- 发送变量变化通知
function other.notify_variable_change(var_name, new_value)
    if other.emit then
        other.emit("variable_changed", {
            var = var_name,
            value = new_value
        })
    end
end

-- 重写变量设置，自动发送通知
local original_A = other.A
local original_B = other.B
local original_C = other.C

-- 创建属性访问器
function other.set_A(value)
    other.A = value
    other.notify_variable_change("A", value)
end

function other.set_B(value)
    other.B = value
    other.notify_variable_change("B", value)
end

function other.set_C(value)
    other.C = value
    other.notify_variable_change("C", value)
end

-- 批量设置变量
function other.set_variables(vars)
    for key, value in pairs(vars) do
        if other[key] ~= nil then
            other[key] = value
            other.notify_variable_change(key, value)
        end
    end
end

-- 获取所有变量
function other.get_all_variables()
    return {
        A = other.A,
        B = other.B,
        C = other.C,
        name = other.name,
        version = other.version
    }
end

-- 模块初始化函数（会被GlobalModuleManager自动调用）
function other.init()
    print("other_global模块正在初始化...")
    other.setup_event_listeners()
    print("other_global模块初始化完成")
end

-- 演示函数：展示所有全局访问功能
function other.demo_global_access()
    print("\n=== other模块全局访问演示 ===")
    
    print("1. 打印自己的变量:")
    other.print_variables()
    
    print("\n2. 访问third模块:")
    other.use_third_variables()
    
    print("\n3. 与third模块协作:")
    other.collaborate_with_third()
    
    print("\n4. 发送事件通知:")
    if other.emit then
        other.emit("demo_event", {message = "来自other模块的问候"})
    end
end

print("other_global.lua 模块已加载")

return other
