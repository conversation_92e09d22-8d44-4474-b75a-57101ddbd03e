# Lua变量共享方案详解

## 问题描述
您想要让 `other_script.lua` 能够直接访问 `main_script.lua` 中的全局变量 A、B、C。

## 解决方案

### 方案1：直接全局变量共享（最简单）

**原理**：Lua中的全局变量在所有脚本间共享。

**实现**：
- `main_script.lua` 设置全局变量 A、B、C
- `other_script.lua` 直接使用这些全局变量

**优点**：简单直接，无需额外代码
**缺点**：可能造成命名冲突，难以管理

### 方案2：纯Lua元方法实现

**原理**：使用Lua的元表机制，重写 `__index` 和 `__newindex` 元方法。

**关键代码**：
```lua
-- __index 元方法：当访问不存在的变量时调用
function SharedVars.__index(t, key)
    if shared_storage[key] ~= nil then
        return shared_storage[key]
    end
    return original_G[key]
end

-- __newindex 元方法：当设置新变量时调用
function SharedVars.__newindex(t, key, value)
    shared_storage[key] = value
    original_G[key] = value
end
```

**优点**：
- 纯Lua实现，无需C++
- 可以监控变量的读写
- 灵活的变量管理

### 方案3：C++扩展实现

**原理**：通过C++编写Lua扩展，实现更强大的变量共享机制。

**关键特性**：
- 类型安全的变量存储
- 多Lua状态间的变量同步
- 更好的性能

**C++核心代码**：
```cpp
// __index 元方法
static int shared_index(lua_State* L) {
    const char* key = lua_tostring(L, 2);
    if (key && shared_vars.find(key) != shared_vars.end()) {
        lua_pushnumber(L, shared_vars[key]);
        return 1;
    }
    lua_pushnil(L);
    return 1;
}

// __newindex 元方法
static int shared_newindex(lua_State* L) {
    const char* key = lua_tostring(L, 2);
    double value = lua_tonumber(L, 3);
    if (key) {
        shared_vars[key] = value;
        // 同时设置到全局环境
        lua_getglobal(L, "_G");
        lua_pushstring(L, key);
        lua_pushnumber(L, value);
        lua_settable(L, -3);
        lua_pop(L, 1);
    }
    return 0;
}
```

## 使用示例

### 使用方案1（直接全局变量）：
```lua
-- main_script.lua
A = 10
B = 20
C = 30

-- other_script.lua
print("A = " .. A)  -- 输出: A = 10
A = 100             -- 修改全局变量
```

### 使用方案2（纯Lua元方法）：
```lua
-- 初始化
local SharedVars = require("shared_vars")
SharedVars.init()

-- 设置变量
A = 10  -- 触发 __newindex

-- 在其他脚本中访问
print(A)  -- 触发 __index，输出: 10
```

### 使用方案3（C++扩展）：
```lua
-- 加载C++扩展
local shared_vars = require("shared_vars")
shared_vars.init()

-- 设置和访问变量
shared_vars.set("A", 10)
local value = shared_vars.get("A")
```

## 编译C++扩展

如果要使用C++方案，需要：

1. 安装Lua开发库
2. 编译共享库：
   ```bash
   # Linux/Mac
   g++ -std=c++11 -fPIC -shared -I/usr/include/lua5.3 -o shared_vars.so lua_shared_vars.cpp -llua5.3
   
   # Windows
   g++ -std=c++11 -shared -I"C:/Program Files/Lua/include" -o shared_vars.dll lua_shared_vars.cpp -L"C:/Program Files/Lua/lib" -llua53
   ```

## 推荐方案

对于您的需求，推荐使用**方案2（纯Lua元方法）**，因为：
1. 无需编译C++代码
2. 功能强大且灵活
3. 易于调试和修改
4. 跨平台兼容性好

## 实际应用

修改后的脚本已经实现了方案1和方案2。您可以：
1. 直接运行查看全局变量共享效果
2. 使用 `shared_vars.lua` 模块获得更高级的变量管理功能
