#!/bin/bash

# Lua Portable 安装脚本 (Linux/Mac)

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTALL_DIR="$SCRIPT_DIR/lua-portable"
LUA_VERSION="5.4.4"

echo "==================================="
echo "Lua Portable 安装脚本"
echo "==================================="
echo
echo "🎯 安装目标: $INSTALL_DIR"
echo "📦 Lua 版本: $LUA_VERSION"
echo

# 检测操作系统
OS="$(uname -s)"
case "$OS" in
    Linux*)     PLATFORM="linux";;
    Darwin*)    PLATFORM="macos";;
    *)          echo "❌ 不支持的操作系统: $OS"; exit 1;;
esac

echo "🖥️  检测到系统: $PLATFORM"

# 创建安装目录
mkdir -p "$INSTALL_DIR"/{bin,lib,include,scripts}

# 检查是否已安装
if [ -f "$INSTALL_DIR/bin/lua" ]; then
    echo "✅ Lua 已安装在 $INSTALL_DIR"
    test_installation
    exit 0
fi

# 检查依赖
check_dependencies() {
    echo "🔍 检查依赖..."
    
    if ! command -v gcc >/dev/null 2>&1; then
        echo "❌ 需要安装 gcc"
        case "$PLATFORM" in
            linux)
                echo "💡 Ubuntu/Debian: sudo apt-get install build-essential"
                echo "💡 CentOS/RHEL: sudo yum groupinstall 'Development Tools'"
                ;;
            macos)
                echo "💡 请安装 Xcode Command Line Tools: xcode-select --install"
                ;;
        esac
        exit 1
    fi
    
    if ! command -v make >/dev/null 2>&1; then
        echo "❌ 需要安装 make"
        exit 1
    fi
    
    echo "✅ 依赖检查通过"
}

# 下载和编译 Lua
install_lua() {
    echo "📥 下载 Lua $LUA_VERSION..."
    
    cd "$INSTALL_DIR"
    
    # 下载源码
    if [ ! -f "lua-$LUA_VERSION.tar.gz" ]; then
        if command -v wget >/dev/null 2>&1; then
            wget "http://www.lua.org/ftp/lua-$LUA_VERSION.tar.gz"
        elif command -v curl >/dev/null 2>&1; then
            curl -O "http://www.lua.org/ftp/lua-$LUA_VERSION.tar.gz"
        else
            echo "❌ 需要 wget 或 curl 来下载文件"
            exit 1
        fi
    fi
    
    echo "📦 解压源码..."
    tar -xzf "lua-$LUA_VERSION.tar.gz"
    
    echo "🔨 编译 Lua..."
    cd "lua-$LUA_VERSION"
    
    # 根据平台选择编译目标
    case "$PLATFORM" in
        linux)
            make linux
            ;;
        macos)
            make macosx
            ;;
    esac
    
    echo "📦 安装到便携目录..."
    make install INSTALL_TOP="$INSTALL_DIR"
    
    # 清理
    cd "$INSTALL_DIR"
    rm -rf "lua-$LUA_VERSION" "lua-$LUA_VERSION.tar.gz"
    
    echo "✅ Lua 编译安装完成"
}

# 创建启动脚本
create_scripts() {
    echo "📝 创建启动脚本..."
    
    # 创建 lua 启动脚本
    cat > "$INSTALL_DIR/lua.sh" << 'EOF'
#!/bin/bash
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
export LUA_PATH="$SCRIPT_DIR/scripts/?.lua;$SCRIPT_DIR/scripts/?/init.lua;$LUA_PATH"
export LUA_CPATH="$SCRIPT_DIR/lib/?.so;$LUA_CPATH"
exec "$SCRIPT_DIR/bin/lua" "$@"
EOF
    chmod +x "$INSTALL_DIR/lua.sh"
    
    # 创建 luac 启动脚本
    cat > "$INSTALL_DIR/luac.sh" << 'EOF'
#!/bin/bash
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
exec "$SCRIPT_DIR/bin/luac" "$@"
EOF
    chmod +x "$INSTALL_DIR/luac.sh"
    
    # 创建环境设置脚本
    cat > "$INSTALL_DIR/set_lua_env.sh" << 'EOF'
#!/bin/bash
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "设置 Lua Portable 环境..."
export PATH="$SCRIPT_DIR:$SCRIPT_DIR/bin:$PATH"
export LUA_PATH="$SCRIPT_DIR/scripts/?.lua;$SCRIPT_DIR/scripts/?/init.lua;$LUA_PATH"
export LUA_CPATH="$SCRIPT_DIR/lib/?.so;$LUA_CPATH"

echo "✅ Lua Portable 环境已设置"
echo "💡 使用方法:"
echo "   lua script.lua"
echo "   luac script.lua"
echo

# 启动新的 shell
exec bash
EOF
    chmod +x "$INSTALL_DIR/set_lua_env.sh"
    
    echo "✅ 启动脚本创建完成"
}

# 创建测试脚本
create_test_script() {
    echo "📝 创建测试脚本..."
    
    cat > "$INSTALL_DIR/scripts/test.lua" << 'EOF'
print("🎉 Lua Portable 安装成功!")
print("Lua 版本: " .. _VERSION)
print("当前目录: " .. (os.getenv("PWD") or "N/A"))
print("LUA_PATH: " .. (package.path or "N/A"))

-- 测试基本功能
local function test_basic()
    print("\n🧪 基本功能测试:")
    
    -- 数学运算
    local result = 2 + 3 * 4
    print("  数学运算: 2 + 3 * 4 = " .. result)
    
    -- 字符串操作
    local str = "Hello, Lua!"
    print("  字符串: " .. str)
    
    -- 表操作
    local t = {1, 2, 3, a = "test"}
    print("  表长度: " .. #t)
    print("  表元素: " .. t.a)
    
    print("✅ 基本功能测试通过")
end

test_basic()
EOF
    
    echo "✅ 测试脚本创建完成"
}

# 创建使用说明
create_readme() {
    echo "📝 创建使用说明..."
    
    cat > "$INSTALL_DIR/README.txt" << EOF
Lua Portable 使用说明
=======================

🚀 快速开始:
1. 运行: source set_lua_env.sh (设置环境)
2. 测试: lua scripts/test.lua

📁 目录结构:
bin/        - Lua 可执行文件
lib/        - Lua 库文件
scripts/    - Lua 脚本文件

💡 使用方法:
./lua.sh script.lua     - 运行脚本
./luac.sh script.lua    - 编译脚本

🔧 环境变量:
LUA_PATH    - Lua 模块搜索路径
LUA_CPATH   - C 模块搜索路径

📦 版本信息:
Lua 版本: $LUA_VERSION
平台: $PLATFORM
安装日期: $(date)
EOF
    
    echo "✅ 使用说明创建完成"
}

# 测试安装
test_installation() {
    echo
    echo "🧪 测试安装..."
    
    if [ -f "$INSTALL_DIR/bin/lua" ]; then
        echo "✅ lua 存在"
        if "$INSTALL_DIR/bin/lua" -v >/dev/null 2>&1; then
            echo "✅ lua 运行正常"
        else
            echo "⚠️  lua 运行异常"
        fi
    else
        echo "❌ lua 不存在"
        return 1
    fi
    
    if [ -f "$INSTALL_DIR/bin/luac" ]; then
        echo "✅ luac 存在"
    else
        echo "❌ luac 不存在"
    fi
}

# 主安装流程
main() {
    check_dependencies
    install_lua
    create_scripts
    create_test_script
    create_readme
    test_installation
    
    echo
    echo "🎉 Lua Portable 安装完成!"
    echo
    echo "📍 安装位置: $INSTALL_DIR"
    echo "🚀 启动方式: source $INSTALL_DIR/set_lua_env.sh"
    echo "🧪 测试命令: lua scripts/test.lua"
    echo "📖 使用说明: 查看 $INSTALL_DIR/README.txt"
    echo
    
    # 询问是否立即测试
    read -p "是否立即测试 Lua? (y/n): " choice
    case "$choice" in
        y|Y)
            echo
            echo "🧪 运行测试..."
            "$INSTALL_DIR/bin/lua" "$INSTALL_DIR/scripts/test.lua"
            echo
            ;;
    esac
}

# 运行主程序
main "$@"
