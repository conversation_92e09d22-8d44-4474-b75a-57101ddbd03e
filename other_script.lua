-- 另一个Lua脚本文件 - 模块化版本
-- 创建模块表
local other = {}

-- 模块内部的变量
other.A = 100
other.B = 200
other.C = 300
other.name = "other_script"
other.version = "1.0"

-- 模块函数：显示当前模块的变量值
function other.print_variables()
    print("=== other模块中的变量 ===")
    print("other.A = " .. other.A)
    print("other.B = " .. other.B)
    print("other.C = " .. other.C)
    print("other.name = " .. other.name)
    print("other.version = " .. other.version)
end

-- 模块计算函数：对A、B、C进行一些计算
function other.calculate()
    local sum = other.A + other.B + other.C
    local product = other.A * other.B * other.C

    print("other.A + other.B + other.C = " .. sum)
    print("other.A * other.B * other.C = " .. product)

    return {sum = sum, product = product}
end

-- 获取变量函数：返回当前模块的变量值
function other.get_variables()
    return {A = other.A, B = other.B, C = other.C}
end

-- 修改变量函数
function other.set_variables(new_A, new_B, new_C)
    if new_A then other.A = new_A end
    if new_B then other.B = new_B end
    if new_C then other.C = new_C end

    print("other模块变量已更新:")
    other.print_variables()
end

-- 批量设置变量
function other.set_all(values)
    for key, value in pairs(values) do
        if other[key] ~= nil then
            other[key] = value
        end
    end
end

-- 获取所有变量
function other.get_all()
    return {
        A = other.A,
        B = other.B,
        C = other.C,
        name = other.name,
        version = other.version
    }
end

-- 脚本被加载时自动执行
print("other_script.lua 模块已被加载")
other.print_variables()

-- 返回模块表
return other
