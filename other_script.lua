-- 另一个Lua脚本文件
-- 定义局部变量A、B、C
local A = 100
local B = 200
local C = 300

-- 打印函数：显示当前脚本的变量值
function print_variables()
    print("=== 另一个脚本中的变量 ===")
    print("局部变量 A = " .. A)
    print("局部变量 B = " .. B)
    print("局部变量 C = " .. C)
end

-- 计算函数：对A、B、C进行一些计算
function calculate()
    local sum = A + B + C
    local product = A * B * C
    
    print("A + B + C = " .. sum)
    print("A * B * C = " .. product)
    
    return {sum = sum, product = product}
end

-- 获取变量函数：返回当前脚本的变量值
function get_variables()
    return {A = A, B = B, C = C}
end

-- 修改变量函数
function set_variables(new_A, new_B, new_C)
    if new_A then A = new_A end
    if new_B then B = new_B end
    if new_C then C = new_C end
    
    print("变量已更新:")
    print_variables()
end

-- 脚本被加载时自动执行
print("other_script.lua 已被加载")
print_variables()
calculate()

-- 返回一个包含所有函数的表，供主脚本使用
return {
    print_variables = print_variables,
    calculate = calculate,
    get_variables = get_variables,
    set_variables = set_variables,
    A = A,
    B = B,
    C = C
}
