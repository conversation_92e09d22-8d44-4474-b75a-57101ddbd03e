# -*- coding: utf-8 -*-
import ctypes
import time
import tkinter as tk
from inspect import istraceback
from time import sleep
from tkinter import messagebox
import random

from pymodbus.client import ModbusTcpClient
from tkinter import messagebox
import datetime
from datetime import datetime

import os
import socket
import json
import struct

portID = 1
funWCode = 16
funRCode = 3
regFirstAddr=1106
regModeHead='112233445566'

conWriteerr=11  #写异常
conReadErr=22   #读异常
conFuncErr=33  #抛出异常
DeviceData={}
DeviceOtherData={}

###############################版本号记录########################################################
#20250630_ short模式下,有些变量未赋值引用报错修改

########################################################################################

# 创建一个隐藏的主窗口
#root = tk.Tk()
#root.withdraw()

#写日志文件
def __writeToLog(tempstr):
    try:
        slog_path = os.getcwd()
        slog_path = slog_path + '\pyLog'
        if not os.path.exists(slog_path):
            os.makedirs(slog_path, exist_ok=True)

        now = datetime.now()
        # 格式化时间为字符串
        formatted_time = now.strftime('%Y-%m-%d') + '_E5000.log'

        slog_path = slog_path + '\\' + formatted_time

        # 打开文件，模式为 'w' 表示写模式，如果文件已存在则会被覆盖
        with open(slog_path, 'a', encoding='utf-8') as file:
            # 写入文本到文件
            now = datetime.now()
            # 格式化时间为字符串
            sformatted_time = now.strftime('%Y-%m-%d %H:%M:%S.%f  ')
            file.write(sformatted_time + tempstr + '\n')
    except Exception as e:
        errorStr = str(e)
        return (conFuncErr, errorStr, True)


def __Device_ErrorWrite(seq_context,WriteMode,varvalue):
    try:
        ivarvalue=int(varvalue)
        if seq_context is not None:
            if WriteMode==0:
                seq_context.SetValNumber("StationGlobals.Devices.低压直流负载.DeviceState", 1, ivarvalue)
            elif WriteMode==1:
                seq_context.SetValNumber("StationGlobals.Devices.低压直流负载.DeviceState", 1, ivarvalue)
                #seq_context.SetValNumber("StationGlobals.Devices.低压直流负载.WriteState", 1, ivarvalue)
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__Device_ErrorWrite error ' + errorStr)
        return (conFuncErr, errorStr, True)

# 创建socket连接
def __createSocket(DevIP, DevPort):
	for i in range(3):
		try:
			session = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
			session.settimeout(0.5)  # 设置超时时间  5秒
			session.connect((DevIP, int(DevPort)))
			return session
		except Exception as e:
			__writeToLog('createSocket error:' + str(e))
			return None
		continue

# #打开设备连接
# eg DevIP='127.0.0.1' ;  DevPort=502
def __openDevice(DevIP, DevPort):
	try:
		session = __createSocket(DevIP, DevPort)
		return session
	except Exception as e:
		errorStr = str(e)
		__writeToLog('openDevice error' + errorStr)
		return None


# 关闭设备连接
def __closeDevice(session):
	try:
		session.close()
	except Exception as e:
		errorStr = str(e)
		__writeToLog('closeDevice connect error' + errorStr)

def __hextoSignedNumber(varHex):
    try:
        packed_data = bytes.fromhex(varHex)
        decimal_number_struct = struct.unpack('>i', packed_data)
        return decimal_number_struct[0]
    except Exception as e:
        errorStr = str(e)
        __writeToLog('hextoSignedNumber error', errorStr)
        return (conFuncErr, errorStr, True)

#一个数据占2个寄存器  1_低在前， 其他_高在后
def __DealRegInfoArray(VarregDataS,varMode):
    try:
        VarregDataArray=[]
        for ielment in VarregDataS:
            HValue, LValue = __valueToHiLow(ielment)
            if varMode==1:
                VarregDataArray.append(LValue)
                VarregDataArray.append(HValue)
            else:
                VarregDataArray.append(HValue)
                VarregDataArray.append(LValue)
        return VarregDataArray
    except Exception as e:
        errorStr = str(e)
        __writeToLog('GetRegDataOfRegInfo error' + errorStr)
        return VarregDataArray

def __GetRegDataOfRegInfo(VarregDataS,VarregDataArray):
    try:
        for ielment in VarregDataS:
            HValue=int(ielment)
            sHex = format(HValue, '04x')
            VarregDataArray.append(sHex)
        return (conFuncErr, '', False)
    except Exception as e:
        errorStr = str(e)
        __writeToLog('GetRegDataOfRegInfo error' + errorStr)
        return (conFuncErr, errorStr, True)

#多寄存器
def __regInfoTocmdxx(var_regInfo,var_messages,var_retmessages):
    try:
        if 'regMode' in  var_regInfo:
            regMode=1    #内部协议
        else:
            regMode=0

        regAddr=var_regInfo['regAddr']
        regNum=var_regInfo['regNum']
        regByte=(regNum*2)
        regData=var_regInfo['regData']
        regLen=(7+regByte)
        iregData=''
        for idata in regData:
            iregData=(iregData+idata)
        sreceiveHex = '00000000' + format(regLen, '04x') + format(portID, '02x') + format(funWCode, '02x') + format(regAddr, '04x')+ format(regNum, '04x')+ format(regByte, '02x')+ iregData
        retHex='00000000' + format(6, '04x')+ format(portID, '02x') + format(funWCode, '02x')+ format(regAddr, '04x')+ format(regNum, '04x')
        if regMode==1:
            sreceiveHex=regModeHead+sreceiveHex

        var_messages.append(sreceiveHex)
        var_retmessages.append(retHex)
    except Exception as e:
        errorStr = str(e)
        __writeToLog('regInfoTocmdxx error' + errorStr)
        return (conFuncErr, errorStr, True)

    #单寄存器
def __regInfoTocmd(var_regInfo,var_messages,var_retmessages):
    try:
        regAddr=var_regInfo['regAddr']
        regNum=var_regInfo['regNum']
        regByte=(regNum*2)
        regData=var_regInfo['regData']
        regLen=(7+regByte)
        sreceiveHex = '00000000' + format(regLen, '04x') + format(portID, '02x') + format(funWCode, '02x') + format(regAddr, '04x')+ format(regNum, '04x')+ format(regByte, '02x')+ format(regData, '04x')
        retHex='00000000' + format(6, '04x')+ format(portID, '02x') + format(funWCode, '02x')+ format(regAddr, '04x')+ format(regNum, '04x')
        var_messages.append(sreceiveHex)
        var_retmessages.append(retHex)
        return (conWriteerr, '', False)
    except Exception as e:
        errorStr = str(e)
        __writeToLog('regInfoTocmd error' + errorStr)
        return (conFuncErr, errorStr, True)

#这个函数就是控制设备的函数模板，用于给设备发送控制命令,只需要替换每个设备的指令即可
def __send_messagexx(seq_context,session,varmessage,varretmessages):
    try:

    #第一步：获取当前自定义工步中设置的参数值
        import win32com


        '''
        Volt_A = seq_context.GetValNumber("Step.Set.Volt_A",0)
        Volt_B = seq_context.GetValNumber("Step.Set.Volt_B", 0)
        Volt_C = seq_context.GetValNumber("Step.Set.Volt_C", 0)
        Phase_A = seq_context.GetValNumber("Step.Set.Phase_A", 0)
        Phase_B = seq_context.GetValNumber("Step.Set.Phase_B", 0)
        Phase_C = seq_context.GetValNumber("Step.Set.Phase_C", 0)
        Freq = seq_context.GetValNumber("Step.Set.Freq", 0)
        '''


        # 根据协议将获取的设置参数值转为报文，假设上面的7个参数根据协议会转为2个报文01 02 03和04 05 06

        # 如果发送失败就重发，最大失败3次
        max_retries = 3
        error_message = ""
        interval = 0.02

        messages = varmessage
        ii=0
        errret=(conWriteerr,'',False)
        for message in messages:
            #sleep(0.01)
            retmessage=varretmessages[ii]  #返回数据
            #print('message',message)
            #print('retmessage', retmessage)
            ii+=1
            byte_message = bytes.fromhex(message.replace(" ", ""))
            #received_message_length = len(byte_message) #我测试的时候设置了服务器回复我发送的报文，所以后面的判断是判断发送报文和回复是否一致，实际要根据协议去写

            retries = 0
            while retries < max_retries:

                session.sendall(byte_message)
                data = session.recv(1024)  # 尝试读取数据

                received_message = data.hex()  # 字节串转换为16进制显示
                #print('Received from serverXX:', received_message)
                #analysisofS7000(received_message)

                #判断回复的报文是否符合预期
                #print('received_message',received_message)
                #print('retmessage',retmessage)
                #if received_message == byte_message:
                if retmessage in received_message:
                    errret = (conWriteerr, '',False )
                    if seq_context is not None:
                        __Device_ErrorWrite(seq_context, 1, 1)
                    break
                else:
                    errret = (conWriteerr, '报文无返回', True)
                    if seq_context is not None:
                        __Device_ErrorWrite(seq_context, 1, 3)

                retries += 1
        return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('send_messagexx error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)



def ts_open_instrument_resource(seq_context,defaultRM,resource_name):#端口号和IP地址
    try:
       retsession=__openDevice(defaultRM,DevPort=int(resource_name))
       if retsession is not None:
           __Device_ErrorWrite(seq_context, 1, 1)
           __Remote_link_device(seq_context, retsession,1)
           return retsession
       else:
           __Device_ErrorWrite(seq_context, 1, 3)
           return None
    except Exception as e:
        errorStr = str(e)
        __writeToLog('ts_open_instrument_resource error' + errorStr)
        return None

#关闭设备
def ts_close_instrument_resource(seq_context,session):
    try:
        if session:
            #session.write_registers(address=2000, values=[0])
            __Remote_link_device(seq_context, session, 0)
            session.close()
        return 1
    except Exception as e:
        errorStr = str(e)
        __writeToLog('close_instrument_resource error ' + errorStr)


#远程设备控制
def __Remote_link_device(seq_context,session,value):
    try:
        messages = []
        retmessages = []
        regInfo = {}
        #模式下cmd
        regInfo['regAddr']=2000
        regInfo['regNum']=1
        regInfo['regData'] = value
        __regInfoTocmd(regInfo,messages,retmessages)
        errret=__send_messagexx(seq_context,session, messages, retmessages)
        return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__Remote_link_device Err ' + errorStr)
        return (conFuncErr, errorStr, True)


#停止设备控制
def __stop_device(seq_context,session):
    try:
        messages = []
        retmessages = []
        regInfo = {}
        #模式下cmd
        regInfo['regAddr']=2021
        regInfo['regNum']=1
        regInfo['regData'] = 0
        __regInfoTocmd(regInfo,messages,retmessages)

        errret=__send_messagexx(seq_context,session, messages, retmessages)
        return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__stop_device Err ' + errorStr)
        return (conFuncErr, errorStr, True)

#停止设备控制
def __run_device(seq_context,session):
    try:
        messages = []
        retmessages = []
        regInfo = {}
        #模式下cmd
        regInfo['regAddr']=2021
        regInfo['regNum']=1
        regInfo['regData'] = 1
        __regInfoTocmd(regInfo,messages,retmessages)

        errret=__send_messagexx(seq_context,session, messages, retmessages)
        return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__run_device Err ' + errorStr)
        return (conFuncErr, errorStr, True)

#设备停止
def ts_stop_device(seq_context,session):
    errret=__stop_device(seq_context,session)
    return errret

#设备运行
def ts_run_device(seq_context,session):
    errret=__run_device(seq_context,session)
    return errret


#复位设备控制
def __reset_device(seq_context,session):
    try:
        messages = []
        retmessages = []
        regInfo = {}
        #模式下cmd
        regInfo['regAddr']=2023
        regInfo['regNum']=1
        regInfo['regData'] = 1
        __regInfoTocmd(regInfo,messages,retmessages)

        errret=__send_messagexx(seq_context,session, messages, retmessages)
        return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('reset_device Err ' + errorStr)
        return (conFuncErr, errorStr, True)

def ts_reset_device(seq_context,session):
    errret=__reset_device(seq_context,session)
    return errret

 #步阶参数设置-模式，电源，电压频率，相位
def ts_Data_Set_By_Step(StepMode,Value,session,seq_context):
    try:
        if session:
            messages = []
            retmessages = []
            regInfo = {}

            #电源相关设置
            if StepMode == "LCC":
                high, low=__split_i32_value(int(Value*10000))

                regInfo['regAddr'] = 2604
                regInfo['regNum'] = 2
                regInfo['regData'] = []
                sHex = format(high, '04x')
                regInfo['regData'].append(sHex)
                sHex = format(low, '04x')
                regInfo['regData'].append(sHex)
                __regInfoTocmdxx(regInfo, messages, retmessages)

            elif StepMode == "LCV":
                high, low=__split_i32_value(int(Value*10000))

                regInfo['regAddr'] = 2656
                regInfo['regNum'] = 2
                regInfo['regData'] = []
                sHex = format(high, '04x')
                regInfo['regData'].append(sHex)
                sHex = format(low, '04x')
                regInfo['regData'].append(sHex)
                __regInfoTocmdxx(regInfo, messages, retmessages)

            elif StepMode == "LCR":
                high, low=__split_i32_value(int(Value*10000))

                regInfo['regAddr'] = 2754
                regInfo['regNum'] = 2
                regInfo['regData'] = []
                sHex = format(high, '04x')
                regInfo['regData'].append(sHex)
                sHex = format(low, '04x')
                regInfo['regData'].append(sHex)
                __regInfoTocmdxx(regInfo, messages, retmessages)

            if len(messages)>0:
                # 下发
                errret = __send_messagexx(seq_context, session, messages, retmessages)
                return errret
            else:
                return (conWriteerr,'',True)
        else:
            errret = (conWriteerr, '无连接', True)
            return errret
    except Exception as e:
        errorStr = str(e)
        __writeToLog('Data_Set_By_Step error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)

#参数设置-模式
def ts_ParasSet_list(seq_context,session):
    try:
        if seq_context is not None:
            #WorkMode = seq_context.GetValNumber("Step.设置.运行模式", 1)#xxx
            #DangWMode = seq_context.GetValNumber("Step.设置.档位选择", 1)#xxx
            #SuduMode= seq_context.GetValNumber("Step.设置.响应速度", 1)  # xxx
            #TriggerMode = seq_context.GetValNumber("Step.设置.触发模式", 1)  # xxx

            #StopTime= seq_context.GetValNumber("Step.设置.停留时间", 1)  # xxx
            #SetValue = seq_context.GetValNumber("Step.设置.设置值", 1)  # xxx
            #CurrMax = seq_context.GetValNumber("Step.设置.电流限值", 1)  # xxx
            #RiseCurr = seq_context.GetValNumber("Step.设置.电流上升斜率", 1)
            #DownCurr = seq_context.GetValNumber("Step.设置.电流下降斜率", 1)
            CircleNum = seq_context.GetValNumber("Step.设置.组循环次数", 1)

            sGroupStr = seq_context.GetValString("Step.设置.GroupStr", 1)  # xxx


        else:
            WorkMode = 0
            DangWMode = 0
            SuduMode= 0
            TriggerMode = 0

            StopTime= 0
            SetValue = 0
            CurrMax = 0
            RiseCurr = 0
            DownCurr = 0
            CircleNum=0
            sGroupStr = '''[{"WorkMode":11,"DangWMode":0, "SuduMode":0,"TriggerMode":0 ,"StopTime":0, "SetValue":0,"CurrMax":0,"RiseCurr":0,"DownCurr":0}
            ,{"WorkMode":11,"DangWMode":0, "SuduMode":0,"TriggerMode":0 ,"StopTime":0, "SetValue":0,"CurrMax":0,"RiseCurr":0,"DownCurr":0}]'''
            #sGroupStr = '''[{"WaveMode_A":0,"StartVoltDCx_A":0, "EndVoltDCx_A":0,"StartVoltACx_A":0 ,"EndVoltACx_A":0}]'''
        CircleNum=int(CircleNum)

        dataCH = json.loads(sGroupStr)
        #########


        ''''
        dataCH=[{},{},{},{}]
        for i in range(len(dataCH)):
            dataCH[i]['WorkMode']=i    # 1_恒流，2CV
            dataCH[i]['DangWMode'] = 1    #1开始
            dataCH[i]['SuduMode'] = 0    #1开始
            dataCH[i]['TriggerMode'] = 0   #1开始
            dataCH[i]['StopTime'] = (i*0.01+3)*1000
            dataCH[i]['SetValue'] = 1
            dataCH[i]['CurrMax'] = 50
            dataCH[i]['RiseCurr'] = 0.2
            dataCH[i]['DownCurr'] = 0.3
        '''

        ##############

        messages = []
        retmessages = []
        regInfo = {}

        # 下发模式参数
        regInfo['regAddr'] = 30100
        regInfo['regNum'] = 1
        regInfo['regMode'] = 1
        regInfo['regData']=[]
        regDataArray = []
        #####  下发循环的参数
        regDataS =  [17]  #list
        __GetRegDataOfRegInfo(regDataS,regDataArray)
        regInfo['regData']=regDataArray
        __regInfoTocmdxx(regInfo, messages, retmessages)
        #

        regInfo['regAddr'] = 14000
        regInfo['regNum'] = 24
        regInfo['regMode'] = 1
        regInfo['regData']=[]
        regDataArray = []
        iDataNum=len(dataCH) #N个Seq.
        for i in range(iDataNum):
            istr=json.dumps(dataCH[i])
            #__writeToLog(str(i)+'__'+istr)

        #####  下发循环的参数
        if CircleNum<0:
            CircleNum=1
        regDataS =  [iDataNum, 1,CircleNum,0]
        regDataS =regDataS+ [iDataNum, 1]
        for i in range(9):
            regDataS = regDataS + [0, 1]
        __GetRegDataOfRegInfo(regDataS,regDataArray)
        regInfo['regData']=regDataArray
        __regInfoTocmdxx(regInfo, messages, retmessages)


        ###########
        sentData=[]
        iaddr=14050
        for i in range(iDataNum):
            if 'WorkMode' not in dataCH[i]:
                dataCH[i]['WorkMode' ]=0
            iValue=dataCH[i]['WorkMode']
            iValue = int(iValue + 1)
            imode=iValue
            iValue = (1 << 8) | iValue  #固定为组1
            sentData.append(iValue)
            if 'DangWMode' not in dataCH[i]:
                dataCH[i]['DangWMode' ]=0
            iValue=dataCH[i]['DangWMode']
            iValue = int(iValue + 1)
            ii=i+1
            iValue = (ii << 8) | iValue  #高字节存放seg.
            sentData.append(iValue)
            if 'SuduMode' not in dataCH[i]:
                dataCH[i]['SuduMode' ]=-1
            iValue=dataCH[i]['SuduMode']
            iValue=int(iValue+1)

            sentData.append(iValue)

            if 'TriggerMode' not in dataCH[i]:
                dataCH[i]['TriggerMode' ]=-1
            iValue=dataCH[i]['TriggerMode']
            iValue = int(iValue + 1)
            sentData.append(iValue)

            if 'StopTime' not in dataCH[i]:
                dataCH[i]['StopTime' ]=0
            iValue=int(dataCH[i]['StopTime']*1000)
            high, low = __split_i32_value(iValue)
            sentData=sentData+[high,low]

            if 'SetValue' not in dataCH[i]:
                dataCH[i]['SetValue' ]=0
            if imode==3: #cp
                iValue = int(dataCH[i]['SetValue'] * 1000)
            else:
                iValue=int(dataCH[i]['SetValue']*10000)
            high, low = __split_i32_value(iValue)
            sentData=sentData+[high,low]

            if imode==2: #CV模式
                if 'CurrMax' not in dataCH[i]:
                    dataCH[i]['CurrMax' ]=0
                iValue=int(dataCH[i]['CurrMax']*10000)
                high, low = __split_i32_value(iValue)
                sentData=sentData+[high,low]

                sentData=sentData+[0,0]
            else:
                if 'RiseCurr' not in dataCH[i]:
                    dataCH[i]['RiseCurr'] = 0
                iValue = int(dataCH[i]['RiseCurr'] * 10000)
                high, low = __split_i32_value(iValue)
                sentData = sentData + [high, low]

                if 'DownCurr' not in dataCH[i]:
                    dataCH[i]['DownCurr'] = 0
                iValue = int(dataCH[i]['DownCurr'] * 10000)
                high, low = __split_i32_value(iValue)
                sentData = sentData + [high, low]

            regDataArray=[]
            __GetRegDataOfRegInfo(sentData, regDataArray)
            regInfo['regData'] = regDataArray
            regInfo['regAddr'] = iaddr
            regInfo['regNum'] = 12
            regInfo['regMode'] = 1

            __regInfoTocmdxx(regInfo, messages, retmessages)

            iaddr = iaddr + 26
            sentData = []

        errret1=__send_messagexx(seq_context,session,messages,retmessages)
        return errret1

    except Exception as e:
        errorStr = str(e)
        __writeToLog('ts_ParasSet_list error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)


#参数设置-模式
def ts_Data_Set_device(seq_context,session):
    try:
        if seq_context is not None:
            Mode_str = seq_context.GetValString("Step.设置.运行模式",'0')#工作模式
            short = seq_context.GetValNumber("Step.设置.负载短路", 0)#短路设置
            CC_Cur_Set = seq_context.GetValNumber("Step.设置.CC电流参数", 0)#CC电流值设定
            # CC_Cur_Up_Ratio = seq_context.GetValNumber("Step.Set.CC_Cur_Up_Ratio", 0)#CC电流上升斜率
            # CC_Cur_Down_Ratio = seq_context.GetValNumber("Step.Set.CC_Cur_Down_Ratio", 0)#CC电流下降斜率
            CV_Vol_Set = seq_context.GetValNumber("Step.设置.CV电压参数", 0)#CV电压值设定
            CV_CurrMax_Set = seq_context.GetValNumber("Step.设置.CV电流限值", 0)  # CV电流限值设定
            CR_Res_Set = seq_context.GetValNumber("Step.设置.CR电阻参数", 0)#CR模式电阻设定
            # CR_Cur_Up_Ratio = seq_context.GetValNumber("Step.设置.CR_Cur_Up_Ratio", 0)#CR模式电流上升斜率
            # CR_Cur_Down_Ratio = seq_context.GetValNumber("Step.设置.CR_Cur_Down_Ratio", 0)#CR模式电流下降斜率
            CCD_Cur_Set_A = seq_context.GetValNumber("Step.设置.A档位负载参数", 0)#CCD模式电流设定A
            CCD_Cur_Set_B = seq_context.GetValNumber("Step.设置.B档位负载参数", 0)#CCD模式电流设定B
            # CCD_Cur_Up_Ratio = seq_context.GetValNumber("Step.设置.CCD_Cur_Up_Ratio", 0)#CCD模式电流上升斜率
            # CCD_Cur_Down_Ratio = seq_context.GetValNumber("Step.设置.CCD_Cur_Down_Ratio", 0)#CCD模式电流下降斜率
            CCD_T1 = seq_context.GetValNumber("Step.设置.A档位拉载时间", 0)#CCD模式T1时间
            CCD_T2= seq_context.GetValNumber("Step.设置.B档位拉载时间", 0)#CCD模式T2时间
            CCD_Repeat_Num= seq_context.GetValNumber("Step.设置.重复次数", 0)#CCD模式重复次数
            CCD_CurrHXL= seq_context.GetValNumber("Step.设置.CCD电流上升斜率", 0)
            CCD_CurrLXL= seq_context.GetValNumber("Step.设置.CCD电流下降斜率", 0)
            #Mode_ACorDC= seq_context.GetValNumber("Step.设置.耦合方式", 0)
            CP_Res_Set = seq_context.GetValNumber("Step.设置.CP功率参数", 0)  # CR模式电阻设定
            if Mode_str=="CC":
               sdangw_1=seq_context.GetValString("Step.设置.CC电流范围", '0')  #
               sdangw_2 = seq_context.GetValString("Step.设置.CC电压量测范围", '0')  #
               if 'CCL'in sdangw_1:
                   dangw_1=1
               elif 'CCM'in sdangw_1:
                   dangw_1 = 2
               else:
                   dangw_1 = 3
            elif Mode_str=="CV":
               sdangw_1=seq_context.GetValString("Step.设置.CV电压范围", 0)  #
               if 'CVL'in sdangw_1:
                   dangw_1=1
               elif 'CVM'in sdangw_1:
                   dangw_1 = 2
               else:
                   dangw_1 = 3
               sdangw_2 = seq_context.GetValString("Step.设置.CV电流量测档位", '0')  #
            elif Mode_str=="CR":
               sdangw_1=seq_context.GetValString("Step.设置.CR电阻范围", 0)  #
               sdangw_2 = seq_context.GetValString("Step.设置.CR电流量测档位", '0')  #
               if 'CRL'in sdangw_1:
                   dangw_1=1
               elif 'CRM'in sdangw_1:
                   dangw_1 = 2
               else:
                   dangw_1 = 3
            elif Mode_str=="CCD":
               sdangw_1=seq_context.GetValString("Step.设置.CCD动态电流范围", '0')  #
               sdangw_2 = seq_context.GetValString("Step.设置.CCD电压量测档位", '0')  #
               if 'CCDL'in sdangw_1:
                   dangw_1=1
               elif 'CCDM'in sdangw_1:
                   dangw_1 = 2
               else:
                   dangw_1 = 3
            elif Mode_str=="CP":
                sdangw_1 = seq_context.GetValString("Step.设置.CP功率范围", '0')  #
                sdangw_2 = seq_context.GetValString("Step.设置.CP电压量测档位", '0')  #
                if 'CPL'in sdangw_1:
                   dangw_1=1
                elif 'CPM'in sdangw_1:
                   dangw_1 = 2
                else:
                   dangw_1 = 3
            else:
                sdangw_1 = 'CCH' #
                sdangw_2 = 'H'  #
                dangw_1 = 3

            if  'L' in sdangw_2:
                dangw_2=1
            elif 'M' in sdangw_2:
                dangw_2=2
            else:
                dangw_2=3
        else:
            Mode_str = "CCD"#工作模式
            short = 0 #短路设置
            CC_Cur_Set = 0 #CC电流值设定
            # CC_Cur_Up_Ratio = seq_context.GetValNumber("Step.Set.CC_Cur_Up_Ratio", 0)#CC电流上升斜率
            # CC_Cur_Down_Ratio = seq_context.GetValNumber("Step.Set.CC_Cur_Down_Ratio", 0)#CC电流下降斜率
            CV_Vol_Set = 0 #CV电压值设定
            CR_Res_Set = 0 #CR模式电阻设定
            # CR_Cur_Up_Ratio = seq_context.GetValNumber("Step.设置.CR_Cur_Up_Ratio", 0)#CR模式电流上升斜率
            # CR_Cur_Down_Ratio = seq_context.GetValNumber("Step.设置.CR_Cur_Down_Ratio", 0)#CR模式电流下降斜率
            CCD_Cur_Set_A = 0 #CCD模式电流设定A
            CCD_Cur_Set_B = 0 #CCD模式电流设定B
            # CCD_Cur_Up_Ratio = seq_context.GetValNumber("Step.设置.CCD_Cur_Up_Ratio", 0)#CCD模式电流上升斜率
            # CCD_Cur_Down_Ratio = seq_context.GetValNumber("Step.设置.CCD_Cur_Down_Ratio", 0)#CCD模式电流下降斜率
            CCD_T1 = 0 #CCD模式T1时间
            CCD_T2= 0 #CCD模式T2时间
            CCD_Repeat_Num= 0 #CCD模式重复次数
            CV_CurrMax_Set=0
            CCD_CurrHXL= 1
            CCD_CurrLXL= 1
            CP_Res_Set=1
            dangw_1 = 3
            dangw_2 = 3
            #Mode_ACorDC=0
        dangw_1 = int(dangw_1)
        dangw_2 = int(dangw_2)
        short=int(short)
        if Mode_str=="CC":
           work_mode=1
        elif Mode_str=="CV":
           work_mode=2
        elif Mode_str=="CR":
           work_mode=4
        elif Mode_str=="CCD":
           work_mode=5
        elif Mode_str == "Short":
            work_mode = 255
        elif Mode_str=="CP":
            work_mode = 3
        else:
            work_mode = 2
        # else:
        #    messagebox.showinfo("提示", "模式设置错误")

        messages = []
        retmessages = []
        regInfo = {}

        if work_mode < 255:
            regInfo['regAddr'] = 2020
            regInfo['regNum'] = 1
            regInfo['regData'] = work_mode
            __regInfoTocmd(regInfo, messages, retmessages)
        if Mode_str == "Short":
            regInfo['regAddr'] = 2022
            regInfo['regNum'] = 1
            regInfo['regData'] = int(short)
            __regInfoTocmd(regInfo, messages, retmessages)
        if Mode_str == "CC":
            regInfo['regAddr'] = 2600
            regInfo['regNum'] = 6
            regInfo['regData']=[]
            regDataArray = []
            regDataS=[dangw_1,dangw_2,1,1]
            high, low = __split_i32_value(int(CC_Cur_Set * 10000))
            regDataS =regDataS+[high, low]
            __GetRegDataOfRegInfo(regDataS,regDataArray)
            regInfo['regData']=regDataArray
            __regInfoTocmdxx(regInfo, messages, retmessages)
        if Mode_str == "CV":
            regInfo['regAddr'] = 2650
            regInfo['regNum'] = 2
            regInfo['regData']=[]
            regDataArray = []
            regDataS=[dangw_1,dangw_2]   #CV模式电压H档位,电流H档位
            __GetRegDataOfRegInfo(regDataS,regDataArray)
            regInfo['regData']=regDataArray
            __regInfoTocmdxx(regInfo, messages, retmessages)

            regInfo['regAddr'] = 2653
            regInfo['regNum'] = 5
            regInfo['regData']=[]
            regDataArray = []
            regDataS=[1]   #CV模式默认A档位
            high, low = __split_i32_value(int(CV_CurrMax_Set * 10000)) #CV模式电流设定值
            regDataS = regDataS+[high, low]

            high, low = __split_i32_value(int(CV_Vol_Set * 10000))#CV模式电流设定值
            regDataS = regDataS+[high, low]

            __GetRegDataOfRegInfo(regDataS,regDataArray)
            regInfo['regData']=regDataArray
            __regInfoTocmdxx(regInfo, messages, retmessages)

        if Mode_str == "CR":
            regInfo['regAddr'] = 2750
            regInfo['regNum'] = 6
            regInfo['regData']=[]
            regDataArray = []
            regDataS=[dangw_1,dangw_2,1,1]   #CR模式电阻H档位 CR模式电流H档位 CR模式默认A档位
            high, low = __split_i32_value(int(CR_Res_Set * 10000))
            regDataS=regDataS+[high, low]
            __GetRegDataOfRegInfo(regDataS,regDataArray)
            regInfo['regData']=regDataArray
            __regInfoTocmdxx(regInfo, messages, retmessages)
        if Mode_str == "CP":
            regInfo['regAddr'] = 2700
            regInfo['regNum'] = 6
            regInfo['regData']=[]
            regDataArray = []
            regDataS=[dangw_1,dangw_2,1,1]   #CR模式电阻H档位 CR模式电流H档位 CR模式默认A档位
            high, low = __split_i32_value(int(CP_Res_Set * 1000))
            regDataS=regDataS+[high, low]
            __GetRegDataOfRegInfo(regDataS,regDataArray)
            regInfo['regData']=regDataArray
            __regInfoTocmdxx(regInfo, messages, retmessages)
        if Mode_str == "CCD":
            regInfo['regAddr'] = 2800
            regInfo['regNum'] =15
            regInfo['regData']=[]
            regDataArray = []
            regDataS=[dangw_1,dangw_2]   #CCD模式电流H档位,CCD模式电压H档位
            high, low = __split_i32_value(int(CCD_Cur_Set_A * 10000))
            regDataS=regDataS+[high, low]
            high, low = __split_i32_value(int(CCD_Cur_Set_B * 10000))
            regDataS = regDataS + [high, low]

            high, low = __split_i32_value(int(CCD_CurrHXL * 10000))
            regDataS = regDataS + [high, low]
            high, low = __split_i32_value(int(CCD_CurrLXL * 10000))
            regDataS = regDataS + [high, low]
            high, low = __split_i32_value(int(CCD_T1 * 1000))
            regDataS = regDataS + [high, low]
            high, low = __split_i32_value(int(CCD_T2 * 1000))
            regDataS = regDataS + [high, low,int(CCD_Repeat_Num)]

            __GetRegDataOfRegInfo(regDataS,regDataArray)
            regInfo['regData']=regDataArray
            __regInfoTocmdxx(regInfo, messages, retmessages)

        errret1=__send_messagexx(seq_context,session,messages,retmessages)
        return errret1

    except Exception as e:
        errorStr = str(e)
        __writeToLog('ts_Data_Set_device error ' + errorStr)
        __Device_ErrorWrite(seq_context, 1, 2)
        return (conFuncErr, errorStr, True)

def __analysisofDevice(dataHex):
    try:
        global  portID
        global  funRCode
        global  regFirstAddr
        global  regNums
        sreceiveHex=dataHex

        hexLen=(3+regNums*2)
        dataBytes=regNums*2
        if 1>=10:
            ###########以下模拟数据##############
            sreceiveHex = '00000000' +format(hexLen, '04x')+format(portID, '02x')+format(funRCode, '02x')+format(dataBytes, '02x')+'fefefefefefefe'
            ihex=''
            for i in range(43):
                ihex=ihex+format(i, '04x')
            sreceiveHex=sreceiveHex+ihex
            #print(ihex)
            ###########以上模拟数据##############
        headHex='00000000'+format(hexLen, '04x')+format(portID, '02x')+format(funRCode, '02x')+format(dataBytes, '02x')
        #######查找接收数据是否有效以及解析
        headHexIndex=sreceiveHex.find(headHex)
        if headHexIndex!= -1:
            end_index = headHexIndex + len(headHex)
            validData=sreceiveHex[end_index:]  #有效数据
            iaddr=regFirstAddr
            if len(validData)>=2:
                DeviceData['Time'] =datetime.now()
            while len(validData)>=2:
                if (iaddr >=1100)and (iaddr <=1119):
                    hexLo=validData[:4]  #取前4个字符
                    validData = validData[4:]
                    hexHi=validData[:4]
                    validData = validData[4:]
                    hexData= (hexLo+hexHi)
                    #valuedata = int(hexData, 16)
                    valuedata = __hextoSignedNumber(hexData)
                    #print('hexData',hexData)
                    if iaddr==1100:
                        DeviceData['RMSCurr']=(valuedata/10000)
                    if iaddr==1102:
                        DeviceData['RMSVol']=(valuedata/10000)
                    if iaddr == 1104:
                        DeviceData['RMSPo']=(valuedata/1000)

                    if iaddr==1106:
                        DeviceData['PPCurr']=(valuedata/10000)
                    if iaddr==1108:
                        DeviceData['PPVol']=(valuedata/10000)
                    if iaddr == 1110:
                        DeviceData['PPPo']=(valuedata/1000)

                    if iaddr==1112:
                        DeviceData['LLCurr']=(valuedata/10000)
                    if iaddr==1114:
                        DeviceData['LLVol']=(valuedata/10000)
                    if iaddr == 1116:
                        DeviceData['LLPo']=(valuedata/1000)

                    if iaddr == 1118:
                        DeviceData['LLPo']=(valuedata)

                    iaddr=(iaddr+2)
                elif (iaddr >=1180)and (iaddr <=1188):
                    hexHi=validData[:4]  #取前4个字符
                    validData = validData[4:]
                    hexData= (hexHi)
                    valuedata = int(hexData, 16)
                    #print('hexData',hexData)
                    iKey='Fault'+str(iaddr-1180)
                    DeviceData[iKey] =valuedata     #各故障码

                    iaddr = (iaddr +1)
                elif (iaddr ==1160):
                    hexHi=validData[:4]  #取前4个字符
                    validData = validData[4:]
                    hexData= (hexHi)
                    valuedata = int(hexData, 16)

                    DeviceData['LoadState1'] =valuedata     #负载运行状态1
                    DeviceData['运行状态']= bool(valuedata & (1 << 0))

                    iaddr = (iaddr +1)
                elif (iaddr >1100)and (iaddr <=1170):
                    hexHi=validData[:4]  #取前4个字符
                    validData = validData[4:]
                    iaddr = (iaddr + 1)
                else:
                    hexHi=validData[:4]  #取前4个字符
                    validData = validData[4:]
                    valuedata = int(hexHi, 16)
                    DeviceOtherData[str(iaddr)]=valuedata
                    iaddr = (iaddr + 1)
    except Exception as e:
        errorStr = str(e)
        __writeToLog('analysisofDevice error' + errorStr)
        return (conReadErr, errorStr, True)

def ts_read_data_device(seq_context,session):
    try:
        global portID
        global funRCode
        global regFirstAddr
        global regNums
        # 假设设备的状态查询报文是01 02 03，此处如果查询设备报文需要进行两次，可以写两个函数，然后在teststand中调用，这里不需要重试机制
        retries = 0
        queryRet=0
        while retries < 4:
            retries+=1
            if retries == 1:
                regFirstAddr=1180
                regNums = 8
            elif retries == 2:
                regFirstAddr=1100
                regNums = 61
            elif retries == 3:
                regFirstAddr=2020    #14050,12
                regNums = 23
            else:
                regFirstAddr=2020
                regNums=3

            hexLen=6
            headHex = '00000000' + format(hexLen, '04x') + format(portID, '02x') + format(funRCode, '02x') + format(regFirstAddr, '04x')+ format(regNums, '04x')
            message=headHex
            interval = 0.02

            byte_message = bytes.fromhex(message.replace(" ", ""))

            session.sendall(byte_message)
            received_message = session.recv(1024)  # 尝试读取数据
            if received_message:
                received_message = received_message.hex()
            else:
                received_message=''


            if len(received_message)>0:
                #print('received_message',received_message)
                __analysisofDevice(received_message) #解析数据
                #赋值到teststand
                #messagebox.showinfo('0000')
                if (seq_context is not None) and (regFirstAddr==1100)and ('RMSCurr'in DeviceData):
                    #messagebox.showinfo('0000_')
                    seq_context.SetValBoolean("StationGlobals.Devices.低压直流负载.状态.运行状态", True, DeviceData['运行状态'])
                    seq_context.SetValNumber("StationGlobals.Devices.低压直流负载.状态.电流", 1, DeviceData['RMSCurr'])
                    seq_context.SetValNumber("StationGlobals.Devices.低压直流负载.状态.电压", 1, DeviceData['RMSVol'])
                    seq_context.SetValNumber("StationGlobals.Devices.低压直流负载.状态.功率", 1, DeviceData['RMSPo'])

                    #messagebox.showinfo('111')


                    __Device_ErrorWrite(seq_context, 0, 1)
                    queryRet=(queryRet | 1)


                if (seq_context is not None) and (regFirstAddr == 1180)and ('Fault0'in DeviceData):
                    #messagebox.showinfo('333')
                    #故障解析
                    str_error=''
                    istr0= __read_error_String(0,DeviceData['Fault0'])
                    str_error +=istr0
                    istr0= __read_error_String(0,DeviceData['Fault1'])
                    str_error +=istr0
                    istr0= __read_error_String(0,DeviceData['Fault4'])
                    str_error +=istr0
                    istr0= __read_error_String(0,DeviceData['Fault7'])
                    str_error +=istr0
                    #messagebox.showinfo('222')
                    seq_context.SetValString("StationGlobals.Devices.低压直流负载.状态.故障信息", '1', str_error)
                    if str_error == '':
                        seq_context.SetValBoolean("StationGlobals.Devices.低压直流负载.状态.故障状态", True, False)
                    else:
                        seq_context.SetValBoolean("StationGlobals.Devices.低压直流负载.状态.故障状态", True, True)
                    queryRet = (queryRet | 2)
                    #messagebox.showinfo('444')

        #print('DeviceData',DeviceData)
        #print('DeviceOtherData', DeviceOtherData)

        #messagebox.showinfo( S7000Data['Vol'])
        #if ('Time'in S7000Data )and (S7000Data['Time']!=ioldtime):
        if queryRet !=3:
            return (conReadErr, str(queryRet),True)
        else:
            return (conReadErr, str(queryRet),False)
        #print('received_message',received_message)
        #判断回复的报文是否符合预期,如果符合预期就将报文解析为状态传给teststand，此处我为了测试就直接给tetstsand随机数
        #seq_context.SetValNumber("StationGlobals.Devices.G6000.State1", 0, random.random()
    except Exception as e:
        errorStr = str(e)
        __writeToLog('query error' + errorStr)
        if seq_context is not None:
            __Device_ErrorWrite(seq_context, 0, 2)
        return (conFuncErr, errorStr,True)


def __read_error_String(errMode,Value):
    try:
        str_error=''
        if errMode==0:
            Load_Error01=Value
            bit_values = [1 if Load_Error01 & (1 << i) else 0 for i in range(10)]
            for index, item in enumerate(bit_values):
                if item == 1:
                    if index == 0:
                        str_error += "Load_Error01_bit0 OCP1\n"
                    elif index == 1:
                        str_error += "Load_Error01_bit1 OVP1\n"
                    elif index == 2:
                        str_error += "Load_Error01_bit2 OPP1\n"
                    elif index == 3:
                        str_error += "Load_Error01_bit3 RVP\n"
                    elif index == 4:
                        str_error += "Load_Error01_bit4 OCP2\n"
                    elif index == 5:
                        str_error += "Load_Error01_bit5 OVP2\n"
                    elif index == 6:
                        str_error += "Load_Error01_bit6 OPP2\n"
                    elif index == 7:
                        str_error += "Load_Error01_bit7 OCP_User\n"
                    elif index == 8:
                        str_error += "Load_Error01_bit8 OPP_User\n"
                    elif index == 9:
                        str_error += "Load_Error01_bit9 OVP_User\n"

        if errMode == 1:
            Load_Error02=value  # read_result.registers[1]
            bit_values = [1 if Load_Error02 & (1 << i) else 0 for i in range(6)]
            for index, item in enumerate(bit_values):
                if item == 1:
                    if index == 0:
                        str_error += "Load_Error02_bit0 OCP超并机系统限值1.0X\n"
                    elif index == 1:
                        str_error += "Load_Error02_bit1 OVP超并机系统限值1.0X\n"
                    elif index == 2:
                        str_error += "Load_Error02_bit2 OPP超并机系统限值1.0X\n"
                    elif index == 3:
                        str_error += "Load_Error02_bit3 OCP Parallel User\n"
                    elif index == 4:
                        str_error += "Load_Error02_bit4 OVP Parallel User\n"
                    elif index == 5:
                        str_error += "Load_Error02_bit5 OPP Parallel User\n"

        if errMode == 4:
            Sys_Error01=value #read_result.registers[4]
            bit_values = [1 if Sys_Error01 & (1 << i) else 0 for i in range(5)]
            for index, item in enumerate(bit_values):
                if item == 1:
                    if index == 0:
                        str_error += "Load_Sys_Error01_bit0 环境低温报警\n"
                    elif index == 1:
                        str_error += "Load_Sys_Error01_bit1 环境高温报警\n"
                    elif index == 2:
                        str_error += "Load_Sys_Error01_bit2 功率高温报警\n"
                    elif index == 3:
                        str_error += "Load_Sys_Error01_bit3 风机故障报警\n"

        if errMode == 7:
            Slave_Error01=value #read_result.registers[7]
            bit_values = [1 if Slave_Error01 & (1 << i) else 0 for i in range(13)]
            for index, item in enumerate(bit_values):
                if item == 1:
                    if index == 0:
                        str_error += "Slave_Error01_bit0 从机地址缺失(自恢复)\n"
                    elif index == 1:
                        str_error += "Slave_Error01_bit1 从机地址超限(自恢复)\n"
                    elif index == 2:
                        str_error += "Slave_Error01_bit2 从机重名(自恢复)\n"
                    elif index == 3:
                        str_error += "Slave_Error01_bit3 从机广播接收数据超时(自恢复)\n"
                    elif index == 4:
                        str_error += "Slave_Error01_bit4 从机上传接收校验错误\n"
                    elif index == 5:
                        str_error += "Slave_Error01_bit5 从机广播接收校验错误\n"
                    elif index == 7:
                        str_error += "Slave_Error01_bit7 风机故障报警\n"
                    elif index == 8:
                        str_error += "Slave_Error01_bit8 温度报警(包含高温报警和低温报警)\n"
                    elif index == 9:
                        str_error += "Slave_Error01_bit9 RVP\n"
                    elif index == 10:
                        str_error += "Slave_Error01_bit10 OPP\n"
                    elif index == 12:
                        str_error += "Slave_Error01_bit12 OCP\n"

        return  str_error
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__read_error_device error ' + errorStr)
        return ''

def __split_i32_value(value):
    try:
    #将 32 位有符号整数拆分为高 16 位和低 16 位
        high = (value >> 16) & 0xFFFF
        low = value & 0xFFFF
        return high, low
    except Exception as e:
        errorStr = str(e)
        __writeToLog('__split_i32_value error ' + errorStr)

def exsample():
#-------测试专用-发布时屏蔽-------------------
    #Client=ts_open_instrument_resource(None,'192.168.1.122',502)
    Client=ts_open_instrument_resource(None,'192.168.1.122',502)
    #Client=ts_open_instrument_resource(None,'127.0.0.1',502)
    #ts_run_device(None,Client)
    ts_stop_device(None,Client)
    ts_reset_device(None,Client)

    #ts_Data_Set_device(None,Client)


    ts_ParasSet_list(None, Client)
    ts_read_data_device(None, Client)
    ts_run_device(None,Client)

    #sleep(1)
    ts_close_instrument_resource(None,Client)


    #Client=open_instrument_resource(None,'127.0.0.1',502)
    #run_device(None,Client)

    #time.sleep(1)
    #close_instrument_resource(Client)
#exsample()

