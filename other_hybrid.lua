-- other模块 - 混合方案（推荐）
-- 结合模块表和直接变量访问的优势

print("=== other_hybrid.lua 开始加载 ===")

-- 创建模块表（保留命名空间）
local other = {}

-- 方式3：模块表 + 直接属性访问
other.A = 100
other.B = 200
other.C = 300
other.name = "other_hybrid"
other.version = "3.0"

-- 同时创建全局变量的别名（可选）
_G.other_A = other.A
_G.other_B = other.B
_G.other_C = other.C

print("设置了模块变量和全局别名")

-- 模块函数
function other.print_variables()
    print("=== other模块变量（混合版本）===")
    print("other.A = " .. other.A)
    print("other.B = " .. other.B)
    print("other.C = " .. other.C)
    print("other.name = " .. other.name)
end

function other.calculate()
    local sum = other.A + other.B + other.C
    local product = other.A * other.B * other.C
    
    print("other模块计算结果:")
    print("sum = " .. sum)
    print("product = " .. product)
    
    return {sum = sum, product = product}
end

function other.use_third_variables()
    print("=== other模块访问third模块 ===")
    
    if _G.third then
        print("访问 third.X = " .. third.X)
        print("访问 third.Y = " .. third.Y)
        
        -- 修改third模块的变量
        third.X = third.X + 75
        print("修改third.X为: " .. third.X)
        
        -- 调用third的函数
        if third.calculate then
            third.calculate()
        end
    else
        print("third模块未找到")
    end
end

function other.collaborate_with_third()
    print("=== other与third协作（混合版本）===")
    
    if not _G.third then
        print("third模块未找到")
        return 0
    end
    
    -- 数据交换
    local temp = other.A
    other.A = third.X
    third.X = temp
    
    -- 同步全局别名
    _G.other_A = other.A
    
    print("交换变量: other.A <-> third.X")
    print("现在 other.A = " .. other.A)
    print("现在 third.X = " .. third.X)
    
    return other.A + other.B + other.C
end

-- 提供多种访问方式
function other.demo_access_methods()
    print("=== 演示多种访问方式 ===")
    
    print("1. 通过模块表访问:")
    print("  other.A = " .. other.A)
    
    print("2. 通过全局别名访问:")
    print("  other_A = " .. _G.other_A)
    
    print("3. 修改测试:")
    other.A = 999
    _G.other_A = other.A  -- 同步
    print("  修改后 other.A = " .. other.A)
    print("  修改后 other_A = " .. _G.other_A)
end

-- 变量同步函数
function other.sync_global_aliases()
    _G.other_A = other.A
    _G.other_B = other.B
    _G.other_C = other.C
    print("全局别名已同步")
end

-- 批量设置变量
function other.set_variables(vars)
    for key, value in pairs(vars) do
        if other[key] ~= nil then
            other[key] = value
            -- 同步到全局别名
            if _G["other_" .. key] then
                _G["other_" .. key] = value
            end
        end
    end
    print("批量设置完成")
end

print("other_hybrid.lua 加载完成")

return other
